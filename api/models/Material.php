<?php
require_once __DIR__ . '/../middleware/AuthMiddleware.php';

class Material {
    private $conn;
    private $table = 'materials';

    public function __construct($db) {
        $this->conn = $db;
    }

    public function create($data) {
        $query = "INSERT INTO " . $this->table . " 
                  (id, title, description, type, level_id, course_id, file_url, file_size, mime_type, created_at, updated_at) 
                  VALUES (:id, :title, :description, :type, :level_id, :course_id, :file_url, :file_size, :mime_type, NOW(), NOW())";

        $stmt = $this->conn->prepare($query);

        $data['id'] = AuthMiddleware::generateUUID();

        $stmt->bindParam(':id', $data['id']);
        $stmt->bindParam(':title', $data['title']);
        $stmt->bindParam(':description', $data['description']);
        $stmt->bindParam(':type', $data['type']);
        $stmt->bindParam(':level_id', $data['level_id']);
        $stmt->bindParam(':course_id', $data['course_id']);
        $stmt->bindParam(':file_url', $data['file_url']);
        $stmt->bindParam(':file_size', $data['file_size']);
        $stmt->bindParam(':mime_type', $data['mime_type']);

        if ($stmt->execute()) {
            return $data['id'];
        }

        return false;
    }

    public function getAll($limit = 50, $offset = 0, $type = '', $levelId = '', $courseId = '') {
        $query = "SELECT m.*, l.name as level_name, c.name as course_name
                  FROM " . $this->table . " m
                  LEFT JOIN levels l ON m.level_id = l.id
                  LEFT JOIN courses c ON m.course_id = c.id
                  WHERE 1=1";

        $params = [];

        if (!empty($type)) {
            $query .= " AND m.type = :type";
            $params[':type'] = $type;
        }

        if (!empty($levelId)) {
            $query .= " AND m.level_id = :level_id";
            $params[':level_id'] = $levelId;
        }

        if (!empty($courseId)) {
            $query .= " AND m.course_id = :course_id";
            $params[':course_id'] = $courseId;
        }

        $query .= " ORDER BY m.created_at DESC LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getById($id) {
        $query = "SELECT m.*, l.name as level_name, c.name as course_name
                  FROM " . $this->table . " m
                  LEFT JOIN levels l ON m.level_id = l.id
                  LEFT JOIN courses c ON m.course_id = c.id
                  WHERE m.id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch();
    }

    public function update($id, $data) {
        $fields = [];
        $params = [':id' => $id];

        foreach ($data as $key => $value) {
            $fields[] = "$key = :$key";
            $params[":$key"] = $value;
        }

        $query = "UPDATE " . $this->table . " 
                  SET " . implode(', ', $fields) . ", updated_at = NOW() 
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        return $stmt->execute($params);
    }

    public function delete($id) {
        $query = "DELETE FROM " . $this->table . " WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);

        return $stmt->execute();
    }

    public function getTotalCount($type = '', $levelId = '', $courseId = '') {
        $query = "SELECT COUNT(*) as total FROM " . $this->table . " WHERE 1=1";

        $params = [];

        if (!empty($type)) {
            $query .= " AND type = :type";
            $params[':type'] = $type;
        }

        if (!empty($levelId)) {
            $query .= " AND level_id = :level_id";
            $params[':level_id'] = $levelId;
        }

        if (!empty($courseId)) {
            $query .= " AND course_id = :course_id";
            $params[':course_id'] = $courseId;
        }

        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        $result = $stmt->fetch();

        return $result['total'];
    }

    public function getByLevel($levelId, $limit = 50, $offset = 0) {
        $query = "SELECT m.*, l.name as level_name, c.name as course_name
                  FROM " . $this->table . " m
                  LEFT JOIN levels l ON m.level_id = l.id
                  LEFT JOIN courses c ON m.course_id = c.id
                  WHERE m.level_id = :level_id
                  ORDER BY m.created_at DESC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':level_id', $levelId);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getByCourse($courseId, $limit = 50, $offset = 0) {
        $query = "SELECT m.*, l.name as level_name, c.name as course_name
                  FROM " . $this->table . " m
                  LEFT JOIN levels l ON m.level_id = l.id
                  LEFT JOIN courses c ON m.course_id = c.id
                  WHERE m.course_id = :course_id
                  ORDER BY m.created_at DESC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':course_id', $courseId);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getByType($type, $limit = 50, $offset = 0) {
        $query = "SELECT m.*, l.name as level_name, c.name as course_name
                  FROM " . $this->table . " m
                  LEFT JOIN levels l ON m.level_id = l.id
                  LEFT JOIN courses c ON m.course_id = c.id
                  WHERE m.type = :type
                  ORDER BY m.created_at DESC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':type', $type);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getRecentMaterials($limit = 5) {
        $query = "SELECT m.*, l.name as level_name, c.name as course_name
                  FROM " . $this->table . " m
                  LEFT JOIN levels l ON m.level_id = l.id
                  LEFT JOIN courses c ON m.course_id = c.id
                  ORDER BY m.created_at DESC
                  LIMIT :limit";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }
}
?>
