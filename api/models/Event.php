<?php
require_once __DIR__ . '/../middleware/AuthMiddleware.php';

class Event {
    private $conn;
    private $table = 'events';

    public function __construct($db) {
        $this->conn = $db;
    }

    public function create($data) {
        $query = "INSERT INTO " . $this->table . " 
                  (id, title, description, date, time, category, location, created_at, updated_at) 
                  VALUES (:id, :title, :description, :date, :time, :category, :location, NOW(), NOW())";

        $stmt = $this->conn->prepare($query);

        $data['id'] = AuthMiddleware::generateUUID();

        $stmt->bindParam(':id', $data['id']);
        $stmt->bindParam(':title', $data['title']);
        $stmt->bindParam(':description', $data['description']);
        $stmt->bindParam(':date', $data['date']);
        $stmt->bindParam(':time', $data['time']);
        $stmt->bindParam(':category', $data['category']);
        $stmt->bindParam(':location', $data['location']);

        if ($stmt->execute()) {
            return $data['id'];
        }

        return false;
    }

    public function getAll($limit = 50, $offset = 0, $category = '', $startDate = '', $endDate = '') {
        $query = "SELECT * FROM " . $this->table . " WHERE 1=1";

        $params = [];

        if (!empty($category)) {
            $query .= " AND category = :category";
            $params[':category'] = $category;
        }

        if (!empty($startDate)) {
            $query .= " AND date >= :start_date";
            $params[':start_date'] = $startDate;
        }

        if (!empty($endDate)) {
            $query .= " AND date <= :end_date";
            $params[':end_date'] = $endDate;
        }

        $query .= " ORDER BY date DESC, time DESC LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getById($id) {
        $query = "SELECT * FROM " . $this->table . " WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch();
    }

    public function update($id, $data) {
        $fields = [];
        $params = [':id' => $id];

        foreach ($data as $key => $value) {
            $fields[] = "$key = :$key";
            $params[":$key"] = $value;
        }

        $query = "UPDATE " . $this->table . " 
                  SET " . implode(', ', $fields) . ", updated_at = NOW() 
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        return $stmt->execute($params);
    }

    public function delete($id) {
        $query = "DELETE FROM " . $this->table . " WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);

        return $stmt->execute();
    }

    public function getTotalCount($category = '', $startDate = '', $endDate = '') {
        $query = "SELECT COUNT(*) as total FROM " . $this->table . " WHERE 1=1";

        $params = [];

        if (!empty($category)) {
            $query .= " AND category = :category";
            $params[':category'] = $category;
        }

        if (!empty($startDate)) {
            $query .= " AND date >= :start_date";
            $params[':start_date'] = $startDate;
        }

        if (!empty($endDate)) {
            $query .= " AND date <= :end_date";
            $params[':end_date'] = $endDate;
        }

        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        $result = $stmt->fetch();

        return $result['total'];
    }

    public function getByCategory($category, $limit = 50, $offset = 0) {
        $query = "SELECT * FROM " . $this->table . " 
                  WHERE category = :category
                  ORDER BY date DESC, time DESC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':category', $category);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getUpcoming($limit = 10) {
        $query = "SELECT * FROM " . $this->table . " 
                  WHERE date >= CURDATE()
                  ORDER BY date ASC, time ASC
                  LIMIT :limit";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getByDateRange($startDate, $endDate, $limit = 50, $offset = 0) {
        $query = "SELECT * FROM " . $this->table . " 
                  WHERE date BETWEEN :start_date AND :end_date
                  ORDER BY date DESC, time DESC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':start_date', $startDate);
        $stmt->bindParam(':end_date', $endDate);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getRecentEvents($limit = 5) {
        $query = "SELECT * FROM " . $this->table . " 
                  ORDER BY created_at DESC
                  LIMIT :limit";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }
}
?>
