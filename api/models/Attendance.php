<?php
class Attendance {
    private $db;
    private $table = 'attendance_records';

    public function __construct($db) {
        $this->db = $db;
    }

    public function getAll($courseId = null, $levelId = null) {
        try {
            $sql = "SELECT a.*, s.name as student_name, s.student_id as student_number,
                           c.name as course_name, l.name as level_name
                    FROM {$this->table} a
                    LEFT JOIN students s ON a.student_id = s.id
                    LEFT JOIN courses c ON a.course_id = c.id
                    LEFT JOIN levels l ON a.level_id = l.id
                    WHERE 1=1";
            
            $params = [];
            
            if ($courseId) {
                $sql .= " AND a.course_id = ?";
                $params[] = $courseId;
            }
            
            if ($levelId) {
                $sql .= " AND a.level_id = ?";
                $params[] = $levelId;
            }
            
            $sql .= " ORDER BY a.date DESC, s.name ASC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting attendance records: " . $e->getMessage());
            return [];
        }
    }

    public function getById($id) {
        try {
            $sql = "SELECT a.*, s.name as student_name, s.student_id as student_number,
                           c.name as course_name, l.name as level_name
                    FROM {$this->table} a
                    LEFT JOIN students s ON a.student_id = s.id
                    LEFT JOIN courses c ON a.course_id = c.id
                    LEFT JOIN levels l ON a.level_id = l.id
                    WHERE a.id = ?";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$id]);
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting attendance record: " . $e->getMessage());
            return null;
        }
    }

    public function getByDate($date, $courseId = null, $levelId = null) {
        try {
            $sql = "SELECT a.*, s.name as student_name, s.student_id as student_number,
                           c.name as course_name, l.name as level_name
                    FROM {$this->table} a
                    LEFT JOIN students s ON a.student_id = s.id
                    LEFT JOIN courses c ON a.course_id = c.id
                    LEFT JOIN levels l ON a.level_id = l.id
                    WHERE a.date = ?";
            
            $params = [$date];
            
            if ($courseId) {
                $sql .= " AND a.course_id = ?";
                $params[] = $courseId;
            }
            
            if ($levelId) {
                $sql .= " AND a.level_id = ?";
                $params[] = $levelId;
            }
            
            $sql .= " ORDER BY s.name ASC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting attendance by date: " . $e->getMessage());
            return [];
        }
    }

    public function getByDateRange($startDate, $endDate, $courseId = null, $levelId = null) {
        try {
            $sql = "SELECT a.*, s.name as student_name, s.student_id as student_number,
                           c.name as course_name, l.name as level_name
                    FROM {$this->table} a
                    LEFT JOIN students s ON a.student_id = s.id
                    LEFT JOIN courses c ON a.course_id = c.id
                    LEFT JOIN levels l ON a.level_id = l.id
                    WHERE a.date BETWEEN ? AND ?";
            
            $params = [$startDate, $endDate];
            
            if ($courseId) {
                $sql .= " AND a.course_id = ?";
                $params[] = $courseId;
            }
            
            if ($levelId) {
                $sql .= " AND a.level_id = ?";
                $params[] = $levelId;
            }
            
            $sql .= " ORDER BY a.date DESC, s.name ASC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting attendance by date range: " . $e->getMessage());
            return [];
        }
    }

    public function getByStudentId($studentId) {
        try {
            $sql = "SELECT a.*, s.name as student_name, s.student_id as student_number,
                           c.name as course_name, l.name as level_name
                    FROM {$this->table} a
                    LEFT JOIN students s ON a.student_id = s.id
                    LEFT JOIN courses c ON a.course_id = c.id
                    LEFT JOIN levels l ON a.level_id = l.id
                    WHERE a.student_id = ?
                    ORDER BY a.date DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$studentId]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting student attendance: " . $e->getMessage());
            return [];
        }
    }

    public function getByStudentAndDate($studentId, $date) {
        try {
            $sql = "SELECT * FROM {$this->table} WHERE student_id = ? AND date = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$studentId, $date]);
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting attendance by student and date: " . $e->getMessage());
            return null;
        }
    }

    public function create($data) {
        try {
            $sql = "INSERT INTO {$this->table} (student_id, date, status, notes, course_id, level_id, marked_by, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
            
            $stmt = $this->db->prepare($sql);
            $success = $stmt->execute([
                $data['student_id'],
                $data['date'],
                $data['status'],
                $data['notes'] ?? null,
                $data['course_id'] ?? null,
                $data['level_id'] ?? null,
                $data['marked_by'] ?? null
            ]);
            
            return $success ? $this->db->lastInsertId() : false;
        } catch (PDOException $e) {
            error_log("Error creating attendance record: " . $e->getMessage());
            return false;
        }
    }

    public function update($id, $data) {
        try {
            $fields = [];
            $params = [];
            
            $allowedFields = ['student_id', 'date', 'status', 'notes', 'course_id', 'level_id', 'marked_by'];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $fields[] = "$field = ?";
                    $params[] = $data[$field];
                }
            }
            
            if (empty($fields)) {
                return false;
            }
            
            $fields[] = "updated_at = NOW()";
            $params[] = $id;
            
            $sql = "UPDATE {$this->table} SET " . implode(', ', $fields) . " WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            
            return $stmt->execute($params);
        } catch (PDOException $e) {
            error_log("Error updating attendance record: " . $e->getMessage());
            return false;
        }
    }

    public function delete($id) {
        try {
            $sql = "DELETE FROM {$this->table} WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            
            return $stmt->execute([$id]);
        } catch (PDOException $e) {
            error_log("Error deleting attendance record: " . $e->getMessage());
            return false;
        }
    }
}
?>
