<?php
class Schedule {
    private $db;
    private $table = 'schedules';

    public function __construct($db) {
        $this->db = $db;
    }

    public function getAll() {
        try {
            $sql = "SELECT s.*, t.name as teacher_name, l.name as level_name, c.name as course_name
                    FROM {$this->table} s
                    LEFT JOIN users t ON s.teacher_id = t.id
                    LEFT JOIN levels l ON s.level_id = l.id
                    LEFT JOIN courses c ON l.course_id = c.id
                    ORDER BY s.day_of_week ASC, s.start_time ASC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting schedules: " . $e->getMessage());
            return [];
        }
    }

    public function getById($id) {
        try {
            $sql = "SELECT s.*, t.name as teacher_name, l.name as level_name, c.name as course_name
                    FROM {$this->table} s
                    LEFT JOIN users t ON s.teacher_id = t.id
                    LEFT JOIN levels l ON s.level_id = l.id
                    LEFT JOIN courses c ON l.course_id = c.id
                    WHERE s.id = ?";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$id]);
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting schedule: " . $e->getMessage());
            return null;
        }
    }

    public function getTeacherSchedule($teacherId, $dayOfWeek = null, $excludeId = null) {
        try {
            $sql = "SELECT s.*, t.name as teacher_name, l.name as level_name, c.name as course_name
                    FROM {$this->table} s
                    LEFT JOIN users t ON s.teacher_id = t.id
                    LEFT JOIN levels l ON s.level_id = l.id
                    LEFT JOIN courses c ON l.course_id = c.id
                    WHERE s.teacher_id = ?";
            
            $params = [$teacherId];
            
            if ($dayOfWeek !== null) {
                $sql .= " AND s.day_of_week = ?";
                $params[] = $dayOfWeek;
            }
            
            if ($excludeId) {
                $sql .= " AND s.id != ?";
                $params[] = $excludeId;
            }
            
            $sql .= " ORDER BY s.day_of_week ASC, s.start_time ASC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting teacher schedule: " . $e->getMessage());
            return [];
        }
    }

    public function getLevelSchedule($levelId) {
        try {
            $sql = "SELECT s.*, t.name as teacher_name, l.name as level_name, c.name as course_name
                    FROM {$this->table} s
                    LEFT JOIN users t ON s.teacher_id = t.id
                    LEFT JOIN levels l ON s.level_id = l.id
                    LEFT JOIN courses c ON l.course_id = c.id
                    WHERE s.level_id = ?
                    ORDER BY s.day_of_week ASC, s.start_time ASC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$levelId]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting level schedule: " . $e->getMessage());
            return [];
        }
    }

    public function checkConflicts($teacherId, $dayOfWeek, $startTime, $endTime, $excludeId = null) {
        try {
            $sql = "SELECT COUNT(*) as conflict_count
                    FROM {$this->table}
                    WHERE teacher_id = ? 
                    AND day_of_week = ?
                    AND (
                        (start_time <= ? AND end_time > ?) OR
                        (start_time < ? AND end_time >= ?) OR
                        (start_time >= ? AND end_time <= ?)
                    )";
            
            $params = [$teacherId, $dayOfWeek, $startTime, $startTime, $endTime, $endTime, $startTime, $endTime];
            
            if ($excludeId) {
                $sql .= " AND id != ?";
                $params[] = $excludeId;
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result['conflict_count'] > 0;
        } catch (PDOException $e) {
            error_log("Error checking schedule conflicts: " . $e->getMessage());
            return true; // Return true to prevent scheduling in case of error
        }
    }

    public function create($data) {
        try {
            $sql = "INSERT INTO {$this->table} (level_id, course_name, room, start_time, end_time, day_of_week, teacher_id, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
            
            $stmt = $this->db->prepare($sql);
            $success = $stmt->execute([
                $data['level_id'],
                $data['course_name'],
                $data['room'],
                $data['start_time'],
                $data['end_time'],
                $data['day_of_week'],
                $data['teacher_id']
            ]);
            
            return $success ? $this->db->lastInsertId() : false;
        } catch (PDOException $e) {
            error_log("Error creating schedule: " . $e->getMessage());
            return false;
        }
    }

    public function update($id, $data) {
        try {
            $fields = [];
            $params = [];
            
            $allowedFields = ['level_id', 'course_name', 'room', 'start_time', 'end_time', 'day_of_week', 'teacher_id'];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $fields[] = "$field = ?";
                    $params[] = $data[$field];
                }
            }
            
            if (empty($fields)) {
                return false;
            }
            
            $fields[] = "updated_at = NOW()";
            $params[] = $id;
            
            $sql = "UPDATE {$this->table} SET " . implode(', ', $fields) . " WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            
            return $stmt->execute($params);
        } catch (PDOException $e) {
            error_log("Error updating schedule: " . $e->getMessage());
            return false;
        }
    }

    public function delete($id) {
        try {
            $sql = "DELETE FROM {$this->table} WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            
            return $stmt->execute([$id]);
        } catch (PDOException $e) {
            error_log("Error deleting schedule: " . $e->getMessage());
            return false;
        }
    }
}
?>
