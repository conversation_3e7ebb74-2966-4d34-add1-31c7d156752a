<?php
require_once __DIR__ . '/../middleware/AuthMiddleware.php';

class Assignment {
    private $conn;
    private $table = 'assignments';

    public function __construct($db) {
        $this->conn = $db;
    }

    public function create($data) {
        $query = "INSERT INTO " . $this->table . " 
                  (id, title, description, course_id, level_id, due_date, status, instructions, max_score, created_at, updated_at) 
                  VALUES (:id, :title, :description, :course_id, :level_id, :due_date, :status, :instructions, :max_score, NOW(), NOW())";

        $stmt = $this->conn->prepare($query);

        $data['id'] = AuthMiddleware::generateUUID();

        $stmt->bindParam(':id', $data['id']);
        $stmt->bindParam(':title', $data['title']);
        $stmt->bindParam(':description', $data['description']);
        $stmt->bindParam(':course_id', $data['course_id']);
        $stmt->bindParam(':level_id', $data['level_id']);
        $stmt->bindParam(':due_date', $data['due_date']);
        $stmt->bindParam(':status', $data['status']);
        $stmt->bindParam(':instructions', $data['instructions']);
        $stmt->bindParam(':max_score', $data['max_score']);

        if ($stmt->execute()) {
            return $data['id'];
        }

        return false;
    }

    public function getAll($limit = 50, $offset = 0, $courseId = '', $levelId = '', $status = '') {
        $query = "SELECT a.*, c.name as course_name, l.name as level_name
                  FROM " . $this->table . " a
                  LEFT JOIN courses c ON a.course_id = c.id
                  LEFT JOIN levels l ON a.level_id = l.id
                  WHERE 1=1";

        $params = [];

        if (!empty($courseId)) {
            $query .= " AND a.course_id = :course_id";
            $params[':course_id'] = $courseId;
        }

        if (!empty($levelId)) {
            $query .= " AND a.level_id = :level_id";
            $params[':level_id'] = $levelId;
        }

        if (!empty($status)) {
            $query .= " AND a.status = :status";
            $params[':status'] = $status;
        }

        $query .= " ORDER BY a.due_date DESC LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getById($id) {
        $query = "SELECT a.*, c.name as course_name, l.name as level_name
                  FROM " . $this->table . " a
                  LEFT JOIN courses c ON a.course_id = c.id
                  LEFT JOIN levels l ON a.level_id = l.id
                  WHERE a.id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch();
    }

    public function update($id, $data) {
        $fields = [];
        $params = [':id' => $id];

        foreach ($data as $key => $value) {
            $fields[] = "$key = :$key";
            $params[":$key"] = $value;
        }

        $query = "UPDATE " . $this->table . " 
                  SET " . implode(', ', $fields) . ", updated_at = NOW() 
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        return $stmt->execute($params);
    }

    public function delete($id) {
        $query = "DELETE FROM " . $this->table . " WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);

        return $stmt->execute();
    }

    public function getTotalCount($courseId = '', $levelId = '', $status = '') {
        $query = "SELECT COUNT(*) as total FROM " . $this->table . " WHERE 1=1";

        $params = [];

        if (!empty($courseId)) {
            $query .= " AND course_id = :course_id";
            $params[':course_id'] = $courseId;
        }

        if (!empty($levelId)) {
            $query .= " AND level_id = :level_id";
            $params[':level_id'] = $levelId;
        }

        if (!empty($status)) {
            $query .= " AND status = :status";
            $params[':status'] = $status;
        }

        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        $result = $stmt->fetch();

        return $result['total'];
    }

    public function getByLevel($levelId, $limit = 50, $offset = 0) {
        $query = "SELECT a.*, c.name as course_name, l.name as level_name
                  FROM " . $this->table . " a
                  LEFT JOIN courses c ON a.course_id = c.id
                  LEFT JOIN levels l ON a.level_id = l.id
                  WHERE a.level_id = :level_id
                  ORDER BY a.due_date DESC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':level_id', $levelId);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getByCourse($courseId, $limit = 50, $offset = 0) {
        $query = "SELECT a.*, c.name as course_name, l.name as level_name
                  FROM " . $this->table . " a
                  LEFT JOIN courses c ON a.course_id = c.id
                  LEFT JOIN levels l ON a.level_id = l.id
                  WHERE a.course_id = :course_id
                  ORDER BY a.due_date DESC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':course_id', $courseId);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getUpcoming($limit = 10) {
        $query = "SELECT a.*, c.name as course_name, l.name as level_name
                  FROM " . $this->table . " a
                  LEFT JOIN courses c ON a.course_id = c.id
                  LEFT JOIN levels l ON a.level_id = l.id
                  WHERE a.due_date >= CURDATE() AND a.status = 'active'
                  ORDER BY a.due_date ASC
                  LIMIT :limit";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getOverdue($limit = 10) {
        $query = "SELECT a.*, c.name as course_name, l.name as level_name
                  FROM " . $this->table . " a
                  LEFT JOIN courses c ON a.course_id = c.id
                  LEFT JOIN levels l ON a.level_id = l.id
                  WHERE a.due_date < CURDATE() AND a.status = 'active'
                  ORDER BY a.due_date DESC
                  LIMIT :limit";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getByDateRange($startDate, $endDate, $limit = 50, $offset = 0) {
        $query = "SELECT a.*, c.name as course_name, l.name as level_name
                  FROM " . $this->table . " a
                  LEFT JOIN courses c ON a.course_id = c.id
                  LEFT JOIN levels l ON a.level_id = l.id
                  WHERE a.due_date BETWEEN :start_date AND :end_date
                  ORDER BY a.due_date DESC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':start_date', $startDate);
        $stmt->bindParam(':end_date', $endDate);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }
}
?>
