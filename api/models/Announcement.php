<?php
require_once __DIR__ . '/../middleware/AuthMiddleware.php';

class Announcement {
    private $conn;
    private $table = 'announcements';

    public function __construct($db) {
        $this->conn = $db;
    }

    public function create($data) {
        $query = "INSERT INTO " . $this->table . " 
                  (id, title, content, priority, expires_at, is_general, target_levels, created_at, updated_at) 
                  VALUES (:id, :title, :content, :priority, :expires_at, :is_general, :target_levels, NOW(), NOW())";

        $stmt = $this->conn->prepare($query);

        $data['id'] = AuthMiddleware::generateUUID();

        $stmt->bindParam(':id', $data['id']);
        $stmt->bindParam(':title', $data['title']);
        $stmt->bindParam(':content', $data['content']);
        $stmt->bindParam(':priority', $data['priority']);
        $stmt->bindParam(':expires_at', $data['expires_at']);
        $stmt->bindParam(':is_general', $data['is_general'], PDO::PARAM_BOOL);
        $stmt->bindParam(':target_levels', $data['target_levels']);

        if ($stmt->execute()) {
            return $data['id'];
        }

        return false;
    }

    public function getAll($limit = 50, $offset = 0, $priority = '', $isGeneral = null) {
        $query = "SELECT * FROM " . $this->table . " WHERE 1=1";

        $params = [];

        if (!empty($priority)) {
            $query .= " AND priority = :priority";
            $params[':priority'] = $priority;
        }

        if ($isGeneral !== null) {
            $query .= " AND is_general = :is_general";
            $params[':is_general'] = $isGeneral;
        }

        // Only show non-expired announcements
        $query .= " AND (expires_at IS NULL OR expires_at > NOW())";

        $query .= " ORDER BY created_at DESC LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        $announcements = $stmt->fetchAll();

        // Decode target_levels JSON for each announcement
        foreach ($announcements as &$announcement) {
            if ($announcement['target_levels']) {
                $announcement['target_levels'] = json_decode($announcement['target_levels'], true);
            } else {
                $announcement['target_levels'] = [];
            }
        }

        return $announcements;
    }

    public function getById($id) {
        $query = "SELECT * FROM " . $this->table . " WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        $announcement = $stmt->fetch();

        if ($announcement && $announcement['target_levels']) {
            $announcement['target_levels'] = json_decode($announcement['target_levels'], true);
        } elseif ($announcement) {
            $announcement['target_levels'] = [];
        }

        return $announcement;
    }

    public function update($id, $data) {
        $fields = [];
        $params = [':id' => $id];

        foreach ($data as $key => $value) {
            $fields[] = "$key = :$key";
            $params[":$key"] = $value;
        }

        $query = "UPDATE " . $this->table . " 
                  SET " . implode(', ', $fields) . ", updated_at = NOW() 
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        return $stmt->execute($params);
    }

    public function delete($id) {
        $query = "DELETE FROM " . $this->table . " WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);

        return $stmt->execute();
    }

    public function getTotalCount($priority = '', $isGeneral = null) {
        $query = "SELECT COUNT(*) as total FROM " . $this->table . " WHERE 1=1";

        $params = [];

        if (!empty($priority)) {
            $query .= " AND priority = :priority";
            $params[':priority'] = $priority;
        }

        if ($isGeneral !== null) {
            $query .= " AND is_general = :is_general";
            $params[':is_general'] = $isGeneral;
        }

        // Only count non-expired announcements
        $query .= " AND (expires_at IS NULL OR expires_at > NOW())";

        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        $result = $stmt->fetch();

        return $result['total'];
    }

    public function getByPriority($priority, $limit = 50, $offset = 0) {
        $query = "SELECT * FROM " . $this->table . " 
                  WHERE priority = :priority 
                  AND (expires_at IS NULL OR expires_at > NOW())
                  ORDER BY created_at DESC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':priority', $priority);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        $announcements = $stmt->fetchAll();

        // Decode target_levels JSON for each announcement
        foreach ($announcements as &$announcement) {
            if ($announcement['target_levels']) {
                $announcement['target_levels'] = json_decode($announcement['target_levels'], true);
            } else {
                $announcement['target_levels'] = [];
            }
        }

        return $announcements;
    }

    public function getActiveAnnouncements($limit = 10) {
        $query = "SELECT * FROM " . $this->table . " 
                  WHERE (expires_at IS NULL OR expires_at > NOW())
                  ORDER BY 
                    CASE priority 
                      WHEN 'urgent' THEN 1 
                      WHEN 'high' THEN 2 
                      WHEN 'medium' THEN 3 
                      WHEN 'low' THEN 4 
                    END,
                    created_at DESC
                  LIMIT :limit";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        $announcements = $stmt->fetchAll();

        // Decode target_levels JSON for each announcement
        foreach ($announcements as &$announcement) {
            if ($announcement['target_levels']) {
                $announcement['target_levels'] = json_decode($announcement['target_levels'], true);
            } else {
                $announcement['target_levels'] = [];
            }
        }

        return $announcements;
    }

    public function getForLevel($levelId, $limit = 50, $offset = 0) {
        $query = "SELECT * FROM " . $this->table . " 
                  WHERE (is_general = 1 OR JSON_CONTAINS(target_levels, :level_id))
                  AND (expires_at IS NULL OR expires_at > NOW())
                  ORDER BY 
                    CASE priority 
                      WHEN 'urgent' THEN 1 
                      WHEN 'high' THEN 2 
                      WHEN 'medium' THEN 3 
                      WHEN 'low' THEN 4 
                    END,
                    created_at DESC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':level_id', json_encode($levelId));
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        $announcements = $stmt->fetchAll();

        // Decode target_levels JSON for each announcement
        foreach ($announcements as &$announcement) {
            if ($announcement['target_levels']) {
                $announcement['target_levels'] = json_decode($announcement['target_levels'], true);
            } else {
                $announcement['target_levels'] = [];
            }
        }

        return $announcements;
    }
}
?>
