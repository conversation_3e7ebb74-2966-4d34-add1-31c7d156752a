<?php
require_once __DIR__ . '/../middleware/AuthMiddleware.php';

class User {
    private $conn;
    private $table = 'users';

    public function __construct($db) {
        $this->conn = $db;
    }

    public function create($data) {
        $query = "INSERT INTO " . $this->table . " 
                  (id, name, email, password, role, created_at, updated_at) 
                  VALUES (:id, :name, :email, :password, :role, NOW(), NOW())";

        $stmt = $this->conn->prepare($query);

        // Generate UUID and hash password
        $data['id'] = AuthMiddleware::generateUUID();
        $data['password'] = AuthMiddleware::hashPassword($data['password']);

        $stmt->bindParam(':id', $data['id']);
        $stmt->bindParam(':name', $data['name']);
        $stmt->bindParam(':email', $data['email']);
        $stmt->bindParam(':password', $data['password']);
        $stmt->bindParam(':role', $data['role']);

        if ($stmt->execute()) {
            return $this->getById($data['id']);
        }

        return false;
    }

    public function getAll($limit = 50, $offset = 0) {
        $query = "SELECT id, name, email, role, created_at, updated_at 
                  FROM " . $this->table . " 
                  ORDER BY created_at DESC 
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getById($id) {
        $query = "SELECT id, name, email, role, created_at, updated_at 
                  FROM " . $this->table . " 
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch();
    }

    public function getByEmail($email) {
        $query = "SELECT * FROM " . $this->table . " WHERE email = :email";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':email', $email);
        $stmt->execute();

        return $stmt->fetch();
    }

    public function update($id, $data) {
        $fields = [];
        $params = [':id' => $id];

        foreach ($data as $key => $value) {
            if ($key !== 'id' && $key !== 'password') {
                $fields[] = "$key = :$key";
                $params[":$key"] = $value;
            }
        }

        if (empty($fields)) {
            return false;
        }

        $query = "UPDATE " . $this->table . " 
                  SET " . implode(', ', $fields) . ", updated_at = NOW() 
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        return $stmt->execute($params);
    }

    public function updatePassword($id, $newPassword) {
        $hashedPassword = AuthMiddleware::hashPassword($newPassword);
        
        $query = "UPDATE " . $this->table . " 
                  SET password = :password, updated_at = NOW() 
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->bindParam(':password', $hashedPassword);

        return $stmt->execute();
    }

    public function delete($id) {
        $query = "DELETE FROM " . $this->table . " WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);

        return $stmt->execute();
    }

    public function authenticate($email, $password) {
        $user = $this->getByEmail($email);

        if ($user && AuthMiddleware::verifyPassword($password, $user['password'])) {
            // Remove password from returned data
            unset($user['password']);
            return $user;
        }

        return false;
    }

    public function getByRole($role) {
        $query = "SELECT id, name, email, role, created_at, updated_at 
                  FROM " . $this->table . " 
                  WHERE role = :role 
                  ORDER BY name ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':role', $role);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function search($searchTerm, $limit = 50, $offset = 0) {
        $query = "SELECT id, name, email, role, created_at, updated_at 
                  FROM " . $this->table . " 
                  WHERE name LIKE :search OR email LIKE :search 
                  ORDER BY name ASC 
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $searchParam = "%$searchTerm%";
        $stmt->bindParam(':search', $searchParam);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getTotalCount() {
        $query = "SELECT COUNT(*) as total FROM " . $this->table;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch();
        return $result['total'];
    }

    public function getCountByRole($role) {
        $query = "SELECT COUNT(*) as total FROM " . $this->table . " WHERE role = :role";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':role', $role);
        $stmt->execute();
        $result = $stmt->fetch();
        return $result['total'];
    }

    public function emailExists($email, $excludeId = null) {
        $query = "SELECT COUNT(*) as count FROM " . $this->table . " WHERE email = :email";
        $params = [':email' => $email];

        if ($excludeId) {
            $query .= " AND id != :exclude_id";
            $params[':exclude_id'] = $excludeId;
        }

        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        $result = $stmt->fetch();

        return $result['count'] > 0;
    }

    public function updateLastLogin($id) {
        $query = "UPDATE " . $this->table . "
                  SET last_login = NOW(), updated_at = NOW()
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);

        return $stmt->execute();
    }

    // Teacher-specific methods
    public function getTeachers($searchTerm = '', $courseId = '', $levelId = '', $sortBy = 'name', $sortDirection = 'asc', $limit = null) {
        try {
            $sql = "SELECT u.*,
                           GROUP_CONCAT(DISTINCT tc.course_id) as assigned_courses,
                           GROUP_CONCAT(DISTINCT tl.level_id) as assigned_levels
                    FROM {$this->table} u
                    LEFT JOIN teacher_courses tc ON u.id = tc.teacher_id
                    LEFT JOIN teacher_levels tl ON u.id = tl.teacher_id
                    WHERE u.role = 'teacher'";

            $params = [];

            if (!empty($searchTerm)) {
                $sql .= " AND (u.name LIKE ? OR u.email LIKE ?)";
                $params[] = "%$searchTerm%";
                $params[] = "%$searchTerm%";
            }

            $sql .= " GROUP BY u.id";

            if ($courseId) {
                $sql .= " HAVING FIND_IN_SET(?, assigned_courses)";
                $params[] = $courseId;
            }

            if ($levelId) {
                $sql .= ($courseId ? " AND" : " HAVING") . " FIND_IN_SET(?, assigned_levels)";
                $params[] = $levelId;
            }

            // Add sorting
            $allowedSortFields = ['name', 'email', 'created_at'];
            if (in_array($sortBy, $allowedSortFields)) {
                $sortDirection = strtoupper($sortDirection) === 'DESC' ? 'DESC' : 'ASC';
                $sql .= " ORDER BY u.$sortBy $sortDirection";
            }

            if ($limit) {
                $sql .= " LIMIT ?";
                $params[] = $limit;
            }

            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);

            $teachers = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Convert comma-separated strings to arrays
            foreach ($teachers as &$teacher) {
                $teacher['assigned_courses'] = $teacher['assigned_courses'] ? explode(',', $teacher['assigned_courses']) : [];
                $teacher['assigned_levels'] = $teacher['assigned_levels'] ? explode(',', $teacher['assigned_levels']) : [];
            }

            return $teachers;
        } catch (PDOException $e) {
            error_log("Error getting teachers: " . $e->getMessage());
            return [];
        }
    }

    public function getTeacherById($id) {
        try {
            $sql = "SELECT u.*,
                           GROUP_CONCAT(DISTINCT tc.course_id) as assigned_courses,
                           GROUP_CONCAT(DISTINCT tl.level_id) as assigned_levels
                    FROM {$this->table} u
                    LEFT JOIN teacher_courses tc ON u.id = tc.teacher_id
                    LEFT JOIN teacher_levels tl ON u.id = tl.teacher_id
                    WHERE u.id = ? AND u.role = 'teacher'
                    GROUP BY u.id";

            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$id]);

            $teacher = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($teacher) {
                $teacher['assigned_courses'] = $teacher['assigned_courses'] ? explode(',', $teacher['assigned_courses']) : [];
                $teacher['assigned_levels'] = $teacher['assigned_levels'] ? explode(',', $teacher['assigned_levels']) : [];
            }

            return $teacher;
        } catch (PDOException $e) {
            error_log("Error getting teacher by ID: " . $e->getMessage());
            return null;
        }
    }

    public function createTeacher($data) {
        try {
            $this->conn->beginTransaction();

            // Create user first
            $userId = $this->create($data);

            if (!$userId) {
                $this->conn->rollBack();
                return false;
            }

            // Assign courses if provided
            if (isset($data['assigned_courses']) && is_array($data['assigned_courses'])) {
                foreach ($data['assigned_courses'] as $courseId) {
                    $this->assignCourseToTeacher($userId, $courseId);
                }
            }

            // Assign levels if provided
            if (isset($data['assigned_levels']) && is_array($data['assigned_levels'])) {
                foreach ($data['assigned_levels'] as $levelId) {
                    $this->assignLevelToTeacher($userId, $levelId);
                }
            }

            $this->conn->commit();
            return $userId;
        } catch (PDOException $e) {
            $this->conn->rollBack();
            error_log("Error creating teacher: " . $e->getMessage());
            return false;
        }
    }

    public function updateTeacher($id, $data) {
        try {
            $this->conn->beginTransaction();

            // Update user data
            $success = $this->update($id, $data);

            if (!$success) {
                $this->conn->rollBack();
                return false;
            }

            // Update course assignments if provided
            if (isset($data['assigned_courses']) && is_array($data['assigned_courses'])) {
                // Remove existing assignments
                $sql = "DELETE FROM teacher_courses WHERE teacher_id = ?";
                $stmt = $this->conn->prepare($sql);
                $stmt->execute([$id]);

                // Add new assignments
                foreach ($data['assigned_courses'] as $courseId) {
                    $this->assignCourseToTeacher($id, $courseId);
                }
            }

            // Update level assignments if provided
            if (isset($data['assigned_levels']) && is_array($data['assigned_levels'])) {
                // Remove existing assignments
                $sql = "DELETE FROM teacher_levels WHERE teacher_id = ?";
                $stmt = $this->conn->prepare($sql);
                $stmt->execute([$id]);

                // Add new assignments
                foreach ($data['assigned_levels'] as $levelId) {
                    $this->assignLevelToTeacher($id, $levelId);
                }
            }

            $this->conn->commit();
            return true;
        } catch (PDOException $e) {
            $this->conn->rollBack();
            error_log("Error updating teacher: " . $e->getMessage());
            return false;
        }
    }

    public function deleteTeacher($id) {
        try {
            $this->conn->beginTransaction();

            // Remove course assignments
            $sql = "DELETE FROM teacher_courses WHERE teacher_id = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$id]);

            // Remove level assignments
            $sql = "DELETE FROM teacher_levels WHERE teacher_id = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$id]);

            // Delete user
            $success = $this->delete($id);

            if ($success) {
                $this->conn->commit();
                return true;
            } else {
                $this->conn->rollBack();
                return false;
            }
        } catch (PDOException $e) {
            $this->conn->rollBack();
            error_log("Error deleting teacher: " . $e->getMessage());
            return false;
        }
    }

    public function assignCourseToTeacher($teacherId, $courseId) {
        try {
            $sql = "INSERT IGNORE INTO teacher_courses (teacher_id, course_id, created_at) VALUES (?, ?, NOW())";
            $stmt = $this->conn->prepare($sql);
            return $stmt->execute([$teacherId, $courseId]);
        } catch (PDOException $e) {
            error_log("Error assigning course to teacher: " . $e->getMessage());
            return false;
        }
    }

    public function unassignCourseFromTeacher($teacherId, $courseId) {
        try {
            $sql = "DELETE FROM teacher_courses WHERE teacher_id = ? AND course_id = ?";
            $stmt = $this->conn->prepare($sql);
            return $stmt->execute([$teacherId, $courseId]);
        } catch (PDOException $e) {
            error_log("Error unassigning course from teacher: " . $e->getMessage());
            return false;
        }
    }

    public function assignLevelToTeacher($teacherId, $levelId) {
        try {
            $sql = "INSERT IGNORE INTO teacher_levels (teacher_id, level_id, created_at) VALUES (?, ?, NOW())";
            $stmt = $this->conn->prepare($sql);
            return $stmt->execute([$teacherId, $levelId]);
        } catch (PDOException $e) {
            error_log("Error assigning level to teacher: " . $e->getMessage());
            return false;
        }
    }

    public function unassignLevelFromTeacher($teacherId, $levelId) {
        try {
            $sql = "DELETE FROM teacher_levels WHERE teacher_id = ? AND level_id = ?";
            $stmt = $this->conn->prepare($sql);
            return $stmt->execute([$teacherId, $levelId]);
        } catch (PDOException $e) {
            error_log("Error unassigning level from teacher: " . $e->getMessage());
            return false;
        }
    }
}
?> 