<?php
require_once __DIR__ . '/../controllers/AuthController.php';
require_once __DIR__ . '/../controllers/StudentController.php';
require_once __DIR__ . '/../controllers/CourseController.php';
require_once __DIR__ . '/../controllers/PaymentController.php';
require_once __DIR__ . '/../utils/Response.php';

class ApiRouter {
    public static function route($path, $method, $db) {
        // Remove leading slash
        $path = ltrim($path, '/');
        
        // Split path into segments
        $segments = explode('/', $path);
        $resource = $segments[0] ?? '';
        $id = $segments[1] ?? null;
        $action = $segments[2] ?? null;

        try {
            switch ($resource) {
                case 'auth':
                    // For auth endpoints, the action is in the second segment
                    $authAction = $id; // $id is actually the action for auth routes
                    self::handleAuth($method, $authAction, $db);
                    break;
                    
                case 'students':
                    self::handleStudents($method, $id, $action, $db);
                    break;

                case 'teachers':
                    self::handleTeachers($method, $id, $action, $db);
                    break;

                case 'courses':
                    self::handleCourses($method, $id, $action, $db);
                    break;

                case 'levels':
                    self::handleLevels($method, $id, $action, $db);
                    break;

                case 'attendance':
                    self::handleAttendance($method, $id, $action, $db);
                    break;

                case 'schedules':
                    self::handleSchedules($method, $id, $action, $db);
                    break;

                case 'payments':
                    self::handlePayments($method, $id, $action, $db);
                    break;

                case 'transactions':
                    self::handleTransactions($method, $id, $action, $db);
                    break;
                    
                case 'dashboard':
                    self::handleDashboard($method, $action, $db);
                    break;
                    
                case 'upload':
                    self::handleUpload($method, $action, $db);
                    break;
                    
                case '':
                    Response::success(['message' => 'University Portal API v1.0', 'status' => 'active']);
                    break;
                    
                default:
                    Response::notFound('Endpoint not found');
            }
        } catch (Exception $e) {
            Response::serverError('Internal server error: ' . $e->getMessage());
        }
    }

    private static function handleAuth($method, $action, $db) {
        $controller = new AuthController($db);
        
        switch ($method) {
            case 'POST':
                switch ($action) {
                    case 'login':
                        $controller->login();
                        break;
                    case 'register':
                        $controller->register();
                        break;
                    case 'refresh':
                        $controller->refreshToken();
                        break;
                    case 'logout':
                        $controller->logout();
                        break;
                    default:
                        Response::notFound('Auth endpoint not found');
                }
                break;
                
            case 'GET':
                switch ($action) {
                    case 'me':
                        $controller->getCurrentUser();
                        break;
                    default:
                        Response::notFound('Auth endpoint not found');
                }
                break;
                
            default:
                Response::error('Method not allowed', 405);
        }
    }

    private static function handleStudents($method, $id, $action, $db) {
        $controller = new StudentController($db);

        switch ($method) {
            case 'GET':
                if ($id && $action) {
                    switch ($action) {
                        case 'payments':
                            $controller->getStudentPayments($id);
                            break;
                        case 'transactions':
                            $controller->getStudentTransactions($id);
                            break;
                        case 'attendance':
                            require_once __DIR__ . '/../controllers/AttendanceController.php';
                            $attendanceController = new AttendanceController($db);
                            $attendanceController->getStudentAttendance($id);
                            break;
                        case 'payment-summary':
                            require_once __DIR__ . '/../controllers/PaymentController.php';
                            $paymentController = new PaymentController($db);
                            $paymentController->getStudentPaymentSummary($id);
                            break;
                        default:
                            Response::notFound('Student action not found');
                    }
                } elseif ($id) {
                    $controller->getById($id);
                } else {
                    $controller->getAll();
                }
                break;

            case 'POST':
                if ($id && $action === 'upload-photo') {
                    $controller->uploadPhoto($id);
                } else {
                    $controller->create();
                }
                break;

            case 'PUT':
                if ($id) {
                    $controller->update($id);
                } else {
                    Response::error('Student ID required for update', 400);
                }
                break;

            case 'DELETE':
                if ($id) {
                    $controller->delete($id);
                } else {
                    Response::error('Student ID required for deletion', 400);
                }
                break;

            default:
                Response::error('Method not allowed', 405);
        }
    }

    private static function handleTeachers($method, $id, $action, $db) {
        require_once __DIR__ . '/../controllers/TeacherController.php';
        $controller = new TeacherController($db);

        switch ($method) {
            case 'GET':
                if ($id && $action) {
                    switch ($action) {
                        case 'schedule':
                            $controller->getSchedule($id);
                            break;
                        default:
                            Response::notFound('Teacher action not found');
                    }
                } elseif ($id) {
                    $controller->getById($id);
                } else {
                    $controller->getAll();
                }
                break;

            case 'POST':
                if ($id && $action) {
                    switch ($action) {
                        case 'courses':
                            $controller->assignCourse($id);
                            break;
                        case 'levels':
                            $controller->assignLevel($id);
                            break;
                        default:
                            Response::notFound('Teacher action not found');
                    }
                } else {
                    $controller->create();
                }
                break;

            case 'PUT':
                if ($id) {
                    $controller->update($id);
                } else {
                    Response::error('Teacher ID required for update', 400);
                }
                break;

            case 'DELETE':
                if ($id && $action) {
                    // Handle nested resource deletion
                    $segments = explode('/', $_SERVER['REQUEST_URI']);
                    $resourceType = $segments[count($segments) - 2] ?? '';
                    $resourceId = $segments[count($segments) - 1] ?? '';

                    switch ($resourceType) {
                        case 'courses':
                            $controller->unassignCourse($id, $resourceId);
                            break;
                        case 'levels':
                            $controller->unassignLevel($id, $resourceId);
                            break;
                        default:
                            Response::notFound('Teacher resource not found');
                    }
                } elseif ($id) {
                    $controller->delete($id);
                } else {
                    Response::error('Teacher ID required for deletion', 400);
                }
                break;

            default:
                Response::error('Method not allowed', 405);
        }
    }

    private static function handleCourses($method, $id, $action, $db) {
        $controller = new CourseController($db);
        
        switch ($method) {
            case 'GET':
                if ($id && $action) {
                    switch ($action) {
                        case 'students':
                            $controller->getCourseStudents($id);
                            break;
                        case 'levels':
                            $controller->getCourseLevels($id);
                            break;
                        case 'stats':
                            $controller->getCourseStats($id);
                            break;
                        default:
                            Response::notFound('Course action not found');
                    }
                } elseif ($id) {
                    $controller->getById($id);
                } else {
                    $controller->getAll();
                }
                break;
                
            case 'POST':
                $controller->create();
                break;
                
            case 'PUT':
                if ($id) {
                    $controller->update($id);
                } else {
                    Response::error('Course ID required for update', 400);
                }
                break;
                
            case 'DELETE':
                if ($id) {
                    $controller->delete($id);
                } else {
                    Response::error('Course ID required for deletion', 400);
                }
                break;
                
            default:
                Response::error('Method not allowed', 405);
        }
    }

    private static function handleLevels($method, $id, $action, $db) {
        $controller = new CourseController($db);

        switch ($method) {
            case 'GET':
                if ($id && $action) {
                    switch ($action) {
                        case 'students':
                            $controller->getLevelStudents($id);
                            break;
                        case 'schedules':
                            require_once __DIR__ . '/../controllers/ScheduleController.php';
                            $scheduleController = new ScheduleController($db);
                            $scheduleController->getAll(); // Will filter by level_id in query params
                            break;
                        default:
                            Response::notFound('Level action not found');
                    }
                } elseif ($id) {
                    $controller->getLevelById($id);
                } else {
                    $controller->getAllLevels();
                }
                break;

            case 'POST':
                $controller->createLevel();
                break;

            case 'PUT':
                if ($id) {
                    $controller->updateLevel($id);
                } else {
                    Response::error('Level ID required for update', 400);
                }
                break;

            case 'DELETE':
                if ($id) {
                    $controller->deleteLevel($id);
                } else {
                    Response::error('Level ID required for deletion', 400);
                }
                break;

            default:
                Response::error('Method not allowed', 405);
        }
    }

    private static function handleAttendance($method, $id, $action, $db) {
        require_once __DIR__ . '/../controllers/AttendanceController.php';
        $controller = new AttendanceController($db);

        switch ($method) {
            case 'GET':
                if ($id) {
                    $controller->getById($id);
                } else {
                    $controller->getAll();
                }
                break;

            case 'POST':
                if ($action === 'mark') {
                    $controller->markAttendance();
                } else {
                    $controller->create();
                }
                break;

            case 'PUT':
                if ($id) {
                    $controller->update($id);
                } else {
                    Response::error('Attendance ID required for update', 400);
                }
                break;

            case 'DELETE':
                if ($id) {
                    $controller->delete($id);
                } else {
                    Response::error('Attendance ID required for deletion', 400);
                }
                break;

            default:
                Response::error('Method not allowed', 405);
        }
    }

    private static function handleSchedules($method, $id, $action, $db) {
        require_once __DIR__ . '/../controllers/ScheduleController.php';
        $controller = new ScheduleController($db);

        switch ($method) {
            case 'GET':
                if ($action === 'check-conflicts') {
                    $controller->checkConflicts();
                } elseif ($id) {
                    $controller->getById($id);
                } else {
                    $controller->getAll();
                }
                break;

            case 'POST':
                $controller->create();
                break;

            case 'PUT':
                if ($id) {
                    $controller->update($id);
                } else {
                    Response::error('Schedule ID required for update', 400);
                }
                break;

            case 'DELETE':
                if ($id) {
                    $controller->delete($id);
                } else {
                    Response::error('Schedule ID required for deletion', 400);
                }
                break;

            default:
                Response::error('Method not allowed', 405);
        }
    }

    private static function handlePayments($method, $id, $action, $db) {
        $controller = new PaymentController($db);

        switch ($method) {
            case 'GET':
                if ($id && $action) {
                    switch ($action) {
                        case 'transactions':
                            $controller->getPaymentTransactions($id);
                            break;
                        default:
                            Response::notFound('Payment action not found');
                    }
                } elseif ($id) {
                    $controller->getById($id);
                } else {
                    $controller->getAll();
                }
                break;

            case 'POST':
                if ($action === 'recalculate-status') {
                    $controller->recalculatePaymentStatus();
                } else {
                    $controller->create();
                }
                break;

            case 'PUT':
                if ($id) {
                    $controller->update($id);
                } else {
                    Response::error('Payment ID required for update', 400);
                }
                break;

            case 'DELETE':
                if ($id) {
                    $controller->delete($id);
                } else {
                    Response::error('Payment ID required for deletion', 400);
                }
                break;

            default:
                Response::error('Method not allowed', 405);
        }
    }

    private static function handleTransactions($method, $id, $action, $db) {
        $controller = new PaymentController($db);
        
        switch ($method) {
            case 'GET':
                if ($id) {
                    $controller->getTransactionById($id);
                } else {
                    $controller->getAllTransactions();
                }
                break;
                
            case 'POST':
                $controller->createTransaction();
                break;
                
            default:
                Response::error('Method not allowed', 405);
        }
    }

    private static function handleDashboard($method, $action, $db) {
        switch ($method) {
            case 'GET':
                switch ($action) {
                    case 'stats':
                        self::getDashboardStats($db);
                        break;
                    case 'recent-students':
                        self::getRecentStudents($db);
                        break;
                    case 'recent-payments':
                        self::getRecentPayments($db);
                        break;
                    case 'revenue':
                        self::getRevenueStats($db);
                        break;
                    default:
                        self::getDashboardOverview($db);
                }
                break;
                
            default:
                Response::error('Method not allowed', 405);
        }
    }

    private static function handleUpload($method, $action, $db) {
        if ($method !== 'POST') {
            Response::error('Method not allowed', 405);
        }

        require_once __DIR__ . '/../utils/FileUpload.php';
        
        $uploader = new FileUpload();
        
        if (!isset($_FILES['file'])) {
            Response::error('No file uploaded', 400);
        }

        $result = $uploader->uploadImage($_FILES['file'], 'students');
        
        if ($result['success']) {
            Response::success($result, 'File uploaded successfully');
        } else {
            Response::error($result['message'], 400);
        }
    }

    private static function getDashboardOverview($db) {
        require_once __DIR__ . '/../models/Student.php';
        require_once __DIR__ . '/../models/Course.php';
        require_once __DIR__ . '/../models/Payment.php';
        require_once __DIR__ . '/../models/User.php';

        $studentModel = new Student($db);
        $courseModel = new Course($db);
        $paymentModel = new Payment($db);
        $userModel = new User($db);

        $stats = [
            'total_students' => $studentModel->getTotalCount(),
            'active_students' => $studentModel->getCountByStatus('Active'),
            'total_courses' => $courseModel->getTotalCount(),
            'total_payments' => $paymentModel->getTotalCount(),
            'total_revenue' => $paymentModel->getTotalAmount(),
            'total_users' => $userModel->getTotalCount(),
            'recent_students' => $studentModel->getRecentlyRegistered(7, 5),
            'recent_payments' => $paymentModel->getRecentPayments(5),
            'popular_courses' => $courseModel->getPopularCourses(5)
        ];

        Response::success($stats, 'Dashboard overview retrieved successfully');
    }

    private static function getDashboardStats($db) {
        require_once __DIR__ . '/../models/Student.php';
        require_once __DIR__ . '/../models/Payment.php';

        $studentModel = new Student($db);
        $paymentModel = new Payment($db);

        $stats = [
            'students' => [
                'total' => $studentModel->getTotalCount(),
                'active' => $studentModel->getCountByStatus('Active'),
                'inactive' => $studentModel->getCountByStatus('Inactive'),
                'suspended' => $studentModel->getCountByStatus('Suspended'),
                'graduated' => $studentModel->getCountByStatus('Graduated')
            ],
            'revenue' => [
                'total' => $paymentModel->getTotalAmount(),
                'this_month' => $paymentModel->getTotalAmount(date('Y-m-01'), date('Y-m-t')),
                'today' => $paymentModel->getTotalAmount(date('Y-m-d'), date('Y-m-d 23:59:59')),
                'daily' => $paymentModel->getDailyRevenue(30),
                'monthly' => $paymentModel->getMonthlyRevenue()
            ]
        ];

        Response::success($stats, 'Dashboard statistics retrieved successfully');
    }

    private static function getRecentStudents($db) {
        require_once __DIR__ . '/../models/Student.php';
        
        $studentModel = new Student($db);
        $students = $studentModel->getRecentlyRegistered(30, 10);
        
        Response::success($students, 'Recent students retrieved successfully');
    }

    private static function getRecentPayments($db) {
        require_once __DIR__ . '/../models/Payment.php';
        
        $paymentModel = new Payment($db);
        $payments = $paymentModel->getRecentPayments(10);
        
        Response::success($payments, 'Recent payments retrieved successfully');
    }

    private static function getRevenueStats($db) {
        require_once __DIR__ . '/../models/Payment.php';
        
        $paymentModel = new Payment($db);
        
        $stats = [
            'daily_revenue' => $paymentModel->getDailyRevenue(30),
            'monthly_revenue' => $paymentModel->getMonthlyRevenue(),
            'payment_methods' => $paymentModel->getPaymentMethodStats()
        ];
        
        Response::success($stats, 'Revenue statistics retrieved successfully');
    }
}
?> 