<?php
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../utils/Response.php';
require_once __DIR__ . '/../utils/Validator.php';
require_once __DIR__ . '/../middleware/AuthMiddleware.php';

class UserController {
    private $db;
    private $userModel;

    public function __construct($db) {
        $this->db = $db;
        $this->userModel = new User($db);
    }

    public function getAll() {
        AuthMiddleware::authorize(['super_admin', 'admin']);

        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 50;
        $offset = ($page - 1) * $limit;

        $users = $this->userModel->getAll($limit, $offset);
        $total = $this->userModel->getTotalCount();

        Response::paginated($users, $total, $page, $limit, 'Users retrieved successfully');
    }

    public function getById($id) {
        $user = AuthMiddleware::authorize(['super_admin', 'admin', 'teacher', 'student']);

        // Users can only view their own profile unless they're admin
        if ($user->role !== 'super_admin' && $user->role !== 'admin' && $user->user_id !== $id) {
            Response::forbidden('Access denied');
        }

        $userData = $this->userModel->getById($id);

        if ($userData) {
            // Remove sensitive information
            unset($userData['password']);
            Response::success($userData, 'User retrieved successfully');
        } else {
            Response::notFound('User not found');
        }
    }

    public function create() {
        AuthMiddleware::authorize(['super_admin', 'admin']);

        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            Response::error('Invalid JSON data', 400);
        }

        $validator = new Validator($data);
        $validator->required('name')
                 ->required('email')
                 ->required('password')
                 ->required('role')
                 ->email('email')
                 ->minLength('password', 6)
                 ->in('role', ['super_admin', 'admin', 'teacher', 'student', 'parent']);

        if ($validator->fails()) {
            Response::validationError($validator->getErrors());
        }

        // Check if email already exists
        if ($this->userModel->emailExists($data['email'])) {
            Response::error('Email already exists', 400);
        }

        // Sanitize input
        $userData = [
            'name' => Validator::sanitizeInput($data['name']),
            'email' => Validator::sanitizeInput($data['email']),
            'password' => password_hash($data['password'], PASSWORD_DEFAULT),
            'role' => $data['role'],
            'status' => $data['status'] ?? 'active'
        ];

        $userId = $this->userModel->create($userData);

        if ($userId) {
            $newUser = $this->userModel->getById($userId);
            unset($newUser['password']);
            Response::success($newUser, 'User created successfully', 201);
        } else {
            Response::serverError('Failed to create user');
        }
    }

    public function update($id) {
        $user = AuthMiddleware::authorize(['super_admin', 'admin', 'teacher', 'student']);

        // Users can only update their own profile unless they're admin
        if ($user->role !== 'super_admin' && $user->role !== 'admin' && $user->user_id !== $id) {
            Response::forbidden('Access denied');
        }

        // Check if user exists
        $existingUser = $this->userModel->getById($id);
        if (!$existingUser) {
            Response::notFound('User not found');
        }

        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            Response::error('Invalid JSON data', 400);
        }

        $validator = new Validator($data);
        
        if (isset($data['email'])) {
            $validator->email('email');
        }
        
        if (isset($data['password'])) {
            $validator->minLength('password', 6);
        }
        
        if (isset($data['role'])) {
            // Only admins can change roles
            if ($user->role !== 'super_admin' && $user->role !== 'admin') {
                Response::forbidden('Access denied: Cannot change role');
            }
            $validator->in('role', ['super_admin', 'admin', 'teacher', 'student', 'parent']);
        }

        if ($validator->fails()) {
            Response::validationError($validator->getErrors());
        }

        // Check if email already exists (excluding current user)
        if (isset($data['email']) && $this->userModel->emailExists($data['email'], $id)) {
            Response::error('Email already exists', 400);
        }

        // Sanitize input
        $updateData = [];
        $fields = ['name', 'email', 'status'];

        foreach ($fields as $field) {
            if (isset($data[$field])) {
                $updateData[$field] = Validator::sanitizeInput($data[$field]);
            }
        }

        if (isset($data['password'])) {
            $updateData['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }

        if (isset($data['role']) && ($user->role === 'super_admin' || $user->role === 'admin')) {
            $updateData['role'] = $data['role'];
        }

        if (empty($updateData)) {
            Response::error('No valid fields to update', 400);
        }

        if ($this->userModel->update($id, $updateData)) {
            $updatedUser = $this->userModel->getById($id);
            unset($updatedUser['password']);
            Response::success($updatedUser, 'User updated successfully');
        } else {
            Response::serverError('Failed to update user');
        }
    }

    public function delete($id) {
        AuthMiddleware::authorize(['super_admin', 'admin']);

        // Check if user exists
        $user = $this->userModel->getById($id);
        if (!$user) {
            Response::notFound('User not found');
        }

        // Prevent deletion of super admin
        if ($user['role'] === 'super_admin') {
            Response::error('Cannot delete super admin user', 400);
        }

        if ($this->userModel->delete($id)) {
            Response::success(null, 'User deleted successfully');
        } else {
            Response::serverError('Failed to delete user');
        }
    }
}
?>
