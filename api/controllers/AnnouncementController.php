<?php
require_once __DIR__ . '/../models/Announcement.php';
require_once __DIR__ . '/../utils/Response.php';
require_once __DIR__ . '/../utils/Validator.php';
require_once __DIR__ . '/../middleware/AuthMiddleware.php';

class AnnouncementController {
    private $db;
    private $announcementModel;

    public function __construct($db) {
        $this->db = $db;
        $this->announcementModel = new Announcement($db);
    }

    public function getAll() {
        AuthMiddleware::requireAuth();

        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 50;
        $offset = ($page - 1) * $limit;

        $priority = $_GET['priority'] ?? '';
        $isGeneral = isset($_GET['is_general']) ? filter_var($_GET['is_general'], FILTER_VALIDATE_BOOLEAN) : null;

        $announcements = $this->announcementModel->getAll($limit, $offset, $priority, $isGeneral);
        $total = $this->announcementModel->getTotalCount($priority, $isGeneral);

        Response::paginated($announcements, $total, $page, $limit, 'Announcements retrieved successfully');
    }

    public function getById($id) {
        AuthMiddleware::requireAuth();

        $announcement = $this->announcementModel->getById($id);

        if ($announcement) {
            Response::success($announcement, 'Announcement retrieved successfully');
        } else {
            Response::notFound('Announcement not found');
        }
    }

    public function create() {
        AuthMiddleware::authorize(['super_admin', 'admin', 'teacher']);

        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            Response::error('Invalid JSON data', 400);
        }

        $validator = new Validator($data);
        $validator->required('title')
                 ->required('content')
                 ->required('priority')
                 ->in('priority', ['low', 'medium', 'high', 'urgent']);

        if ($validator->fails()) {
            Response::validationError($validator->getErrors());
        }

        // Sanitize input
        $announcementData = [
            'title' => Validator::sanitizeInput($data['title']),
            'content' => Validator::sanitizeInput($data['content']),
            'priority' => $data['priority'],
            'expires_at' => $data['expires_at'] ?? null,
            'is_general' => isset($data['is_general']) ? filter_var($data['is_general'], FILTER_VALIDATE_BOOLEAN) : false,
            'target_levels' => isset($data['target_levels']) && is_array($data['target_levels']) ? json_encode($data['target_levels']) : null
        ];

        $announcementId = $this->announcementModel->create($announcementData);

        if ($announcementId) {
            $newAnnouncement = $this->announcementModel->getById($announcementId);
            Response::success($newAnnouncement, 'Announcement created successfully', 201);
        } else {
            Response::serverError('Failed to create announcement');
        }
    }

    public function update($id) {
        AuthMiddleware::authorize(['super_admin', 'admin', 'teacher']);

        // Check if announcement exists
        $announcement = $this->announcementModel->getById($id);
        if (!$announcement) {
            Response::notFound('Announcement not found');
        }

        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            Response::error('Invalid JSON data', 400);
        }

        $validator = new Validator($data);
        
        if (isset($data['priority'])) {
            $validator->in('priority', ['low', 'medium', 'high', 'urgent']);
        }

        if ($validator->fails()) {
            Response::validationError($validator->getErrors());
        }

        // Sanitize input
        $updateData = [];
        $fields = ['title', 'content', 'priority', 'expires_at'];

        foreach ($fields as $field) {
            if (isset($data[$field])) {
                if (in_array($field, ['title', 'content'])) {
                    $updateData[$field] = Validator::sanitizeInput($data[$field]);
                } else {
                    $updateData[$field] = $data[$field];
                }
            }
        }

        if (isset($data['is_general'])) {
            $updateData['is_general'] = filter_var($data['is_general'], FILTER_VALIDATE_BOOLEAN);
        }

        if (isset($data['target_levels']) && is_array($data['target_levels'])) {
            $updateData['target_levels'] = json_encode($data['target_levels']);
        }

        if (empty($updateData)) {
            Response::error('No valid fields to update', 400);
        }

        if ($this->announcementModel->update($id, $updateData)) {
            $updatedAnnouncement = $this->announcementModel->getById($id);
            Response::success($updatedAnnouncement, 'Announcement updated successfully');
        } else {
            Response::serverError('Failed to update announcement');
        }
    }

    public function delete($id) {
        AuthMiddleware::authorize(['super_admin', 'admin']);

        // Check if announcement exists
        $announcement = $this->announcementModel->getById($id);
        if (!$announcement) {
            Response::notFound('Announcement not found');
        }

        if ($this->announcementModel->delete($id)) {
            Response::success(null, 'Announcement deleted successfully');
        } else {
            Response::serverError('Failed to delete announcement');
        }
    }
}
?>
