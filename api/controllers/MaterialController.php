<?php
require_once __DIR__ . '/../models/Material.php';
require_once __DIR__ . '/../utils/Response.php';
require_once __DIR__ . '/../utils/Validator.php';
require_once __DIR__ . '/../middleware/AuthMiddleware.php';

class MaterialController {
    private $db;
    private $materialModel;

    public function __construct($db) {
        $this->db = $db;
        $this->materialModel = new Material($db);
    }

    public function getAll() {
        AuthMiddleware::requireAuth();

        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 50;
        $offset = ($page - 1) * $limit;

        $type = $_GET['type'] ?? '';
        $levelId = $_GET['level_id'] ?? '';
        $courseId = $_GET['course_id'] ?? '';

        $materials = $this->materialModel->getAll($limit, $offset, $type, $levelId, $courseId);
        $total = $this->materialModel->getTotalCount($type, $levelId, $courseId);

        Response::paginated($materials, $total, $page, $limit, 'Materials retrieved successfully');
    }

    public function getById($id) {
        AuthMiddleware::requireAuth();

        $material = $this->materialModel->getById($id);

        if ($material) {
            Response::success($material, 'Material retrieved successfully');
        } else {
            Response::notFound('Material not found');
        }
    }

    public function create() {
        AuthMiddleware::authorize(['super_admin', 'admin', 'teacher']);

        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            Response::error('Invalid JSON data', 400);
        }

        $validator = new Validator($data);
        $validator->required('title')
                 ->required('type')
                 ->required('level_id')
                 ->in('type', ['document', 'video', 'audio', 'image', 'link', 'other']);

        if ($validator->fails()) {
            Response::validationError($validator->getErrors());
        }

        // Sanitize input
        $materialData = [
            'title' => Validator::sanitizeInput($data['title']),
            'description' => isset($data['description']) ? Validator::sanitizeInput($data['description']) : null,
            'type' => $data['type'],
            'level_id' => $data['level_id'],
            'course_id' => $data['course_id'] ?? null,
            'file_url' => isset($data['file_url']) ? Validator::sanitizeInput($data['file_url']) : null,
            'file_size' => isset($data['file_size']) ? intval($data['file_size']) : null,
            'mime_type' => isset($data['mime_type']) ? Validator::sanitizeInput($data['mime_type']) : null
        ];

        $materialId = $this->materialModel->create($materialData);

        if ($materialId) {
            $newMaterial = $this->materialModel->getById($materialId);
            Response::success($newMaterial, 'Material created successfully', 201);
        } else {
            Response::serverError('Failed to create material');
        }
    }

    public function update($id) {
        AuthMiddleware::authorize(['super_admin', 'admin', 'teacher']);

        // Check if material exists
        $material = $this->materialModel->getById($id);
        if (!$material) {
            Response::notFound('Material not found');
        }

        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            Response::error('Invalid JSON data', 400);
        }

        $validator = new Validator($data);
        
        if (isset($data['type'])) {
            $validator->in('type', ['document', 'video', 'audio', 'image', 'link', 'other']);
        }

        if ($validator->fails()) {
            Response::validationError($validator->getErrors());
        }

        // Sanitize input
        $updateData = [];
        $fields = ['title', 'description', 'type', 'level_id', 'course_id', 'file_url', 'mime_type'];

        foreach ($fields as $field) {
            if (isset($data[$field])) {
                if (in_array($field, ['title', 'description', 'file_url', 'mime_type'])) {
                    $updateData[$field] = Validator::sanitizeInput($data[$field]);
                } else {
                    $updateData[$field] = $data[$field];
                }
            }
        }

        if (isset($data['file_size'])) {
            $updateData['file_size'] = intval($data['file_size']);
        }

        if (empty($updateData)) {
            Response::error('No valid fields to update', 400);
        }

        if ($this->materialModel->update($id, $updateData)) {
            $updatedMaterial = $this->materialModel->getById($id);
            Response::success($updatedMaterial, 'Material updated successfully');
        } else {
            Response::serverError('Failed to update material');
        }
    }

    public function delete($id) {
        AuthMiddleware::authorize(['super_admin', 'admin', 'teacher']);

        // Check if material exists
        $material = $this->materialModel->getById($id);
        if (!$material) {
            Response::notFound('Material not found');
        }

        if ($this->materialModel->delete($id)) {
            Response::success(null, 'Material deleted successfully');
        } else {
            Response::serverError('Failed to delete material');
        }
    }
}
?>
