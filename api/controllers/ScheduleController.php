<?php
require_once __DIR__ . '/../utils/Response.php';
require_once __DIR__ . '/../middleware/AuthMiddleware.php';

class ScheduleController {
    private $db;
    private $scheduleModel;

    public function __construct($db) {
        $this->db = $db;
        require_once __DIR__ . '/../models/Schedule.php';
        $this->scheduleModel = new Schedule($db);
    }

    public function getAll() {
        try {
            $teacherId = $_GET['teacher_id'] ?? null;
            $levelId = $_GET['level_id'] ?? null;
            $dayOfWeek = $_GET['day_of_week'] ?? null;

            if ($teacherId) {
                $schedules = $this->scheduleModel->getTeacherSchedule($teacherId, $dayOfWeek);
            } elseif ($levelId) {
                $schedules = $this->scheduleModel->getLevelSchedule($levelId);
            } else {
                $schedules = $this->scheduleModel->getAll();
            }

            Response::success($schedules, 'Schedules retrieved successfully');
        } catch (Exception $e) {
            Response::serverError('Failed to retrieve schedules: ' . $e->getMessage());
        }
    }

    public function getById($id) {
        try {
            $schedule = $this->scheduleModel->getById($id);
            if ($schedule) {
                Response::success($schedule, 'Schedule retrieved successfully');
            } else {
                Response::notFound('Schedule not found');
            }
        } catch (Exception $e) {
            Response::serverError('Failed to retrieve schedule: ' . $e->getMessage());
        }
    }

    public function create() {
        try {
            AuthMiddleware::requireAuth();
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                Response::error('Invalid JSON input', 400);
                return;
            }

            // Validate required fields
            $required = ['level_id', 'course_name', 'room', 'start_time', 'end_time', 'day_of_week', 'teacher_id'];
            foreach ($required as $field) {
                if (!isset($input[$field])) {
                    Response::error("Field '$field' is required", 400);
                    return;
                }
            }

            // Check for schedule conflicts
            $hasConflict = $this->scheduleModel->checkConflicts(
                $input['teacher_id'],
                $input['day_of_week'],
                $input['start_time'],
                $input['end_time']
            );

            if ($hasConflict) {
                Response::error('Schedule conflict detected for this teacher at the specified time', 409);
                return;
            }

            $scheduleId = $this->scheduleModel->create($input);
            
            if ($scheduleId) {
                $schedule = $this->scheduleModel->getById($scheduleId);
                Response::success($schedule, 'Schedule created successfully', 201);
            } else {
                Response::error('Failed to create schedule', 500);
            }
        } catch (Exception $e) {
            Response::serverError('Failed to create schedule: ' . $e->getMessage());
        }
    }

    public function update($id) {
        try {
            AuthMiddleware::requireAuth();
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                Response::error('Invalid JSON input', 400);
                return;
            }

            // If updating time or teacher, check for conflicts
            if (isset($input['teacher_id']) || isset($input['day_of_week']) || 
                isset($input['start_time']) || isset($input['end_time'])) {
                
                $currentSchedule = $this->scheduleModel->getById($id);
                if (!$currentSchedule) {
                    Response::notFound('Schedule not found');
                    return;
                }

                $teacherId = $input['teacher_id'] ?? $currentSchedule['teacher_id'];
                $dayOfWeek = $input['day_of_week'] ?? $currentSchedule['day_of_week'];
                $startTime = $input['start_time'] ?? $currentSchedule['start_time'];
                $endTime = $input['end_time'] ?? $currentSchedule['end_time'];

                $hasConflict = $this->scheduleModel->checkConflicts(
                    $teacherId,
                    $dayOfWeek,
                    $startTime,
                    $endTime,
                    $id // Exclude current schedule from conflict check
                );

                if ($hasConflict) {
                    Response::error('Schedule conflict detected for this teacher at the specified time', 409);
                    return;
                }
            }

            $success = $this->scheduleModel->update($id, $input);
            
            if ($success) {
                $schedule = $this->scheduleModel->getById($id);
                Response::success($schedule, 'Schedule updated successfully');
            } else {
                Response::error('Failed to update schedule', 500);
            }
        } catch (Exception $e) {
            Response::serverError('Failed to update schedule: ' . $e->getMessage());
        }
    }

    public function delete($id) {
        try {
            AuthMiddleware::requireAuth();
            
            $success = $this->scheduleModel->delete($id);
            
            if ($success) {
                Response::success(null, 'Schedule deleted successfully');
            } else {
                Response::error('Failed to delete schedule', 500);
            }
        } catch (Exception $e) {
            Response::serverError('Failed to delete schedule: ' . $e->getMessage());
        }
    }

    public function checkConflicts() {
        try {
            $teacherId = $_GET['teacher_id'] ?? null;
            $dayOfWeek = $_GET['day_of_week'] ?? null;
            $startTime = $_GET['start_time'] ?? null;
            $endTime = $_GET['end_time'] ?? null;
            $excludeId = $_GET['exclude_id'] ?? null;

            if (!$teacherId || !$dayOfWeek || !$startTime || !$endTime) {
                Response::error('Missing required parameters: teacher_id, day_of_week, start_time, end_time', 400);
                return;
            }

            $hasConflict = $this->scheduleModel->checkConflicts(
                $teacherId,
                (int)$dayOfWeek,
                $startTime,
                $endTime,
                $excludeId
            );

            Response::success(['has_conflict' => $hasConflict], 'Conflict check completed');
        } catch (Exception $e) {
            Response::serverError('Failed to check conflicts: ' . $e->getMessage());
        }
    }
}
?>
