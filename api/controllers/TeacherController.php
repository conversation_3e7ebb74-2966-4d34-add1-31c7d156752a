<?php
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../utils/Response.php';
require_once __DIR__ . '/../middleware/AuthMiddleware.php';

class TeacherController {
    private $db;
    private $userModel;

    public function __construct($db) {
        $this->db = $db;
        $this->userModel = new User($db);
    }

    public function getAll() {
        try {
            // Get query parameters
            $searchTerm = $_GET['search'] ?? '';
            $courseId = $_GET['course_id'] ?? '';
            $levelId = $_GET['level_id'] ?? '';
            $sortBy = $_GET['sort_by'] ?? 'name';
            $sortDirection = $_GET['sort_direction'] ?? 'asc';
            $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : null;

            $teachers = $this->userModel->getTeachers($searchTerm, $courseId, $levelId, $sortBy, $sortDirection, $limit);
            Response::success($teachers, 'Teachers retrieved successfully');
        } catch (Exception $e) {
            Response::serverError('Failed to retrieve teachers: ' . $e->getMessage());
        }
    }

    public function getById($id) {
        try {
            $teacher = $this->userModel->getTeacherById($id);
            if ($teacher) {
                Response::success($teacher, 'Teacher retrieved successfully');
            } else {
                Response::notFound('Teacher not found');
            }
        } catch (Exception $e) {
            Response::serverError('Failed to retrieve teacher: ' . $e->getMessage());
        }
    }

    public function create() {
        try {
            AuthMiddleware::requireAuth();
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                Response::error('Invalid JSON input', 400);
                return;
            }

            // Validate required fields
            $required = ['name', 'email', 'password'];
            foreach ($required as $field) {
                if (!isset($input[$field]) || empty($input[$field])) {
                    Response::error("Field '$field' is required", 400);
                    return;
                }
            }

            // Set role to teacher
            $input['role'] = 'teacher';
            
            $teacherId = $this->userModel->createTeacher($input);
            
            if ($teacherId) {
                $teacher = $this->userModel->getTeacherById($teacherId);
                Response::success($teacher, 'Teacher created successfully', 201);
            } else {
                Response::error('Failed to create teacher', 500);
            }
        } catch (Exception $e) {
            Response::serverError('Failed to create teacher: ' . $e->getMessage());
        }
    }

    public function update($id) {
        try {
            AuthMiddleware::requireAuth();
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                Response::error('Invalid JSON input', 400);
                return;
            }

            $success = $this->userModel->updateTeacher($id, $input);
            
            if ($success) {
                $teacher = $this->userModel->getTeacherById($id);
                Response::success($teacher, 'Teacher updated successfully');
            } else {
                Response::error('Failed to update teacher', 500);
            }
        } catch (Exception $e) {
            Response::serverError('Failed to update teacher: ' . $e->getMessage());
        }
    }

    public function delete($id) {
        try {
            AuthMiddleware::requireAuth();
            
            $success = $this->userModel->deleteTeacher($id);
            
            if ($success) {
                Response::success(null, 'Teacher deleted successfully');
            } else {
                Response::error('Failed to delete teacher', 500);
            }
        } catch (Exception $e) {
            Response::serverError('Failed to delete teacher: ' . $e->getMessage());
        }
    }

    public function assignCourse($teacherId) {
        try {
            AuthMiddleware::requireAuth();
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!isset($input['course_id'])) {
                Response::error('Course ID is required', 400);
                return;
            }

            $success = $this->userModel->assignCourseToTeacher($teacherId, $input['course_id']);
            
            if ($success) {
                Response::success(null, 'Course assigned to teacher successfully');
            } else {
                Response::error('Failed to assign course to teacher', 500);
            }
        } catch (Exception $e) {
            Response::serverError('Failed to assign course: ' . $e->getMessage());
        }
    }

    public function unassignCourse($teacherId, $courseId) {
        try {
            AuthMiddleware::requireAuth();
            
            $success = $this->userModel->unassignCourseFromTeacher($teacherId, $courseId);
            
            if ($success) {
                Response::success(null, 'Course unassigned from teacher successfully');
            } else {
                Response::error('Failed to unassign course from teacher', 500);
            }
        } catch (Exception $e) {
            Response::serverError('Failed to unassign course: ' . $e->getMessage());
        }
    }

    public function assignLevel($teacherId) {
        try {
            AuthMiddleware::requireAuth();
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!isset($input['level_id'])) {
                Response::error('Level ID is required', 400);
                return;
            }

            $success = $this->userModel->assignLevelToTeacher($teacherId, $input['level_id']);
            
            if ($success) {
                Response::success(null, 'Level assigned to teacher successfully');
            } else {
                Response::error('Failed to assign level to teacher', 500);
            }
        } catch (Exception $e) {
            Response::serverError('Failed to assign level: ' . $e->getMessage());
        }
    }

    public function unassignLevel($teacherId, $levelId) {
        try {
            AuthMiddleware::requireAuth();
            
            $success = $this->userModel->unassignLevelFromTeacher($teacherId, $levelId);
            
            if ($success) {
                Response::success(null, 'Level unassigned from teacher successfully');
            } else {
                Response::error('Failed to unassign level from teacher', 500);
            }
        } catch (Exception $e) {
            Response::serverError('Failed to unassign level: ' . $e->getMessage());
        }
    }

    public function getSchedule($teacherId) {
        try {
            require_once __DIR__ . '/../models/Schedule.php';
            $scheduleModel = new Schedule($this->db);
            
            $dayOfWeek = $_GET['day_of_week'] ?? null;
            $exclude = $_GET['exclude'] ?? null;
            
            $schedules = $scheduleModel->getTeacherSchedule($teacherId, $dayOfWeek, $exclude);
            Response::success($schedules, 'Teacher schedule retrieved successfully');
        } catch (Exception $e) {
            Response::serverError('Failed to retrieve teacher schedule: ' . $e->getMessage());
        }
    }
}
?>
