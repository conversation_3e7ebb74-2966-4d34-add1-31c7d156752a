<?php
require_once __DIR__ . '/../models/Payment.php';
require_once __DIR__ . '/../models/Student.php';
require_once __DIR__ . '/../middleware/AuthMiddleware.php';
require_once __DIR__ . '/../utils/Response.php';
require_once __DIR__ . '/../utils/Validator.php';

class PaymentController {
    private $paymentModel;
    private $transactionModel;
    private $studentModel;
    private $db;

    public function __construct($db) {
        $this->db = $db;
        $this->paymentModel = new Payment($db);
        $this->transactionModel = new Transaction($db);
        $this->studentModel = new Student($db);
    }

    // Payment methods
    public function getAll() {
        AuthMiddleware::authorize(['super_admin', 'admin', 'finance_admin']);

        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 50;
        $offset = ($page - 1) * $limit;

        $search = $_GET['search'] ?? '';
        $status = $_GET['status'] ?? '';
        $start_date = $_GET['start_date'] ?? '';
        $end_date = $_GET['end_date'] ?? '';

        if ($search) {
            $payments = $this->paymentModel->search($search, $limit, $offset);
            $total = count($payments); // Simplified for search
        } elseif ($status) {
            $payments = $this->paymentModel->getByStatus($status, $limit, $offset);
            $total = count($payments); // Simplified
        } elseif ($start_date && $end_date) {
            $payments = $this->paymentModel->getByDateRange($start_date, $end_date, $limit, $offset);
            $total = count($payments); // Simplified
        } else {
            $payments = $this->paymentModel->getAll($limit, $offset);
            $total = $this->paymentModel->getTotalCount();
        }

        Response::paginated($payments, $total, $page, $limit, 'Payments retrieved successfully');
    }

    public function getById($id) {
        AuthMiddleware::authorize(['super_admin', 'admin', 'finance_admin']);

        $payment = $this->paymentModel->getById($id);

        if ($payment) {
            Response::success($payment, 'Payment retrieved successfully');
        } else {
            Response::notFound('Payment not found');
        }
    }

    public function create() {
        $user = AuthMiddleware::authorize(['super_admin', 'admin', 'finance_admin']);

        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            Response::error('Invalid JSON data', 400);
        }

        $validator = new Validator($data);
        $validator->required('student_id')
                 ->required('amount')
                 ->required('payment_method')
                 ->numeric('amount')
                 ->in('payment_method', ['Cash', 'Bank Transfer', 'Credit Card', 'Debit Card', 'Mobile Money', 'Cheque'])
                 ->in('status', ['Pending', 'Completed', 'Failed', 'Cancelled']);

        if ($validator->fails()) {
            Response::validationError($validator->getErrors());
        }

        // Check if student exists
        $student = $this->studentModel->getById($data['student_id']);
        if (!$student) {
            Response::error('Invalid student ID', 400);
        }

        // Sanitize input
        $paymentData = [
            'student_id' => $data['student_id'],
            'amount' => floatval($data['amount']),
            'payment_method' => Validator::sanitizeInput($data['payment_method']),
            'payment_date' => $data['payment_date'] ?? date('Y-m-d H:i:s'),
            'reference_number' => Validator::sanitizeInput($data['reference_number'] ?? ''),
            'status' => $data['status'] ?? 'Completed',
            'notes' => Validator::sanitizeInput($data['notes'] ?? ''),
            'processed_by' => $user->user_id
        ];

        $payment = $this->paymentModel->create($paymentData);

        if ($payment) {
            // Create corresponding transaction
            $transactionData = [
                'payment_id' => $payment['id'],
                'student_id' => $payment['student_id'],
                'type' => 'Payment',
                'amount' => $payment['amount'],
                'description' => 'Payment received via ' . $payment['payment_method'],
                'transaction_date' => $payment['payment_date'],
                'created_by' => $user->user_id
            ];

            $this->transactionModel->create($transactionData);

            // Update student payment status if payment is completed
            if ($payment['status'] === 'Completed') {
                $this->studentModel->update($payment['student_id'], ['payment_status' => 'Paid']);
            }

            Response::success($payment, 'Payment created successfully', 201);
        } else {
            Response::serverError('Failed to create payment');
        }
    }

    public function update($id) {
        $user = AuthMiddleware::authorize(['super_admin', 'admin', 'finance_admin']);

        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            Response::error('Invalid JSON data', 400);
        }

        // Check if payment exists
        $existingPayment = $this->paymentModel->getById($id);
        if (!$existingPayment) {
            Response::notFound('Payment not found');
        }

        $validator = new Validator($data);
        
        if (isset($data['amount'])) {
            $validator->numeric('amount');
        }
        
        if (isset($data['payment_method'])) {
            $validator->in('payment_method', ['Cash', 'Bank Transfer', 'Credit Card', 'Debit Card', 'Mobile Money', 'Cheque']);
        }
        
        if (isset($data['status'])) {
            $validator->in('status', ['Pending', 'Completed', 'Failed', 'Cancelled']);
        }

        if ($validator->fails()) {
            Response::validationError($validator->getErrors());
        }

        // Sanitize input
        $updateData = [];
        $fields = ['payment_method', 'payment_date', 'status', 'notes'];

        foreach ($fields as $field) {
            if (isset($data[$field])) {
                $updateData[$field] = Validator::sanitizeInput($data[$field]);
            }
        }

        if (isset($data['amount'])) {
            $updateData['amount'] = floatval($data['amount']);
        }

        if (empty($updateData)) {
            Response::error('No valid fields to update', 400);
        }

        if ($this->paymentModel->update($id, $updateData)) {
            $updatedPayment = $this->paymentModel->getById($id);
            
            // Update student payment status if status changed to completed
            if (isset($updateData['status']) && $updateData['status'] === 'Completed') {
                $this->studentModel->update($updatedPayment['student_id'], ['payment_status' => 'Paid']);
            }

            Response::success($updatedPayment, 'Payment updated successfully');
        } else {
            Response::serverError('Failed to update payment');
        }
    }

    public function delete($id) {
        AuthMiddleware::authorize(['super_admin', 'admin']);

        // Check if payment exists
        $payment = $this->paymentModel->getById($id);
        if (!$payment) {
            Response::notFound('Payment not found');
        }

        if ($this->paymentModel->delete($id)) {
            Response::success(null, 'Payment deleted successfully');
        } else {
            Response::serverError('Failed to delete payment');
        }
    }

    public function getPaymentTransactions($id) {
        AuthMiddleware::authorize(['super_admin', 'admin', 'finance_admin']);

        // Check if payment exists
        $payment = $this->paymentModel->getById($id);
        if (!$payment) {
            Response::notFound('Payment not found');
        }

        $transactions = $this->transactionModel->getByPayment($id);

        Response::success([
            'payment' => $payment,
            'transactions' => $transactions
        ], 'Payment transactions retrieved successfully');
    }

    // Transaction methods
    public function getAllTransactions() {
        AuthMiddleware::authorize(['super_admin', 'admin', 'finance_admin']);

        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 50;
        $offset = ($page - 1) * $limit;

        $transactions = $this->transactionModel->getAll($limit, $offset);
        $total = $this->transactionModel->getTotalCount();

        Response::paginated($transactions, $total, $page, $limit, 'Transactions retrieved successfully');
    }

    public function getTransactionById($id) {
        AuthMiddleware::authorize(['super_admin', 'admin', 'finance_admin']);

        $transaction = $this->transactionModel->getById($id);

        if ($transaction) {
            Response::success($transaction, 'Transaction retrieved successfully');
        } else {
            Response::notFound('Transaction not found');
        }
    }

    public function createTransaction() {
        $user = AuthMiddleware::authorize(['super_admin', 'admin', 'finance_admin']);

        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            Response::error('Invalid JSON data', 400);
        }

        $validator = new Validator($data);
        $validator->required('student_id')
                 ->required('type')
                 ->required('amount')
                 ->required('description')
                 ->numeric('amount')
                 ->in('type', ['Payment', 'Refund', 'Fee', 'Adjustment']);

        if ($validator->fails()) {
            Response::validationError($validator->getErrors());
        }

        // Check if student exists
        $student = $this->studentModel->getById($data['student_id']);
        if (!$student) {
            Response::error('Invalid student ID', 400);
        }

        // Sanitize input
        $transactionData = [
            'payment_id' => $data['payment_id'] ?? null,
            'student_id' => $data['student_id'],
            'type' => Validator::sanitizeInput($data['type']),
            'amount' => floatval($data['amount']),
            'description' => Validator::sanitizeInput($data['description']),
            'transaction_date' => $data['transaction_date'] ?? date('Y-m-d H:i:s'),
            'created_by' => $user->user_id
        ];

        $transaction = $this->transactionModel->create($transactionData);

        if ($transaction) {
            Response::success($transaction, 'Transaction created successfully', 201);
        } else {
            Response::serverError('Failed to create transaction');
        }
    }

    public function getPaymentStats() {
        AuthMiddleware::authorize(['super_admin', 'admin', 'finance_admin']);

        $start_date = $_GET['start_date'] ?? null;
        $end_date = $_GET['end_date'] ?? null;

        $stats = [
            'total_amount' => $this->paymentModel->getTotalAmount($start_date, $end_date),
            'payment_methods' => $this->paymentModel->getPaymentMethodStats($start_date, $end_date),
            'daily_revenue' => $this->paymentModel->getDailyRevenue(30),
            'monthly_revenue' => $this->paymentModel->getMonthlyRevenue()
        ];

        Response::success($stats, 'Payment statistics retrieved successfully');
    }

    public function getRecentPayments() {
        AuthMiddleware::authorize(['super_admin', 'admin', 'finance_admin']);

        $limit = isset($_GET['limit']) ? min(50, max(1, intval($_GET['limit']))) : 10;

        $payments = $this->paymentModel->getRecentPayments($limit);

        Response::success($payments, 'Recent payments retrieved successfully');
    }

    public function getPaymentsByStudent() {
        AuthMiddleware::authorize(['super_admin', 'admin', 'finance_admin']);

        $student_id = $_GET['student_id'] ?? '';
        
        if (empty($student_id)) {
            Response::error('Student ID parameter is required', 400);
        }

        // Check if student exists
        $student = $this->studentModel->getById($student_id);
        if (!$student) {
            Response::notFound('Student not found');
        }

        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 50;
        $offset = ($page - 1) * $limit;

        $payments = $this->paymentModel->getByStudent($student_id, $limit, $offset);
        $summary = $this->paymentModel->getStudentPaymentSummary($student_id);

        Response::success([
            'student' => $student,
            'payments' => $payments,
            'summary' => $summary
        ], 'Student payments retrieved successfully');
    }

    public function searchPayments() {
        AuthMiddleware::authorize(['super_admin', 'admin', 'finance_admin']);

        $search = $_GET['search'] ?? '';
        
        if (empty($search)) {
            Response::error('Search parameter is required', 400);
        }

        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 50;
        $offset = ($page - 1) * $limit;

        $payments = $this->paymentModel->search($search, $limit, $offset);

        Response::success($payments, 'Payment search results retrieved successfully');
    }

    public function getStudentPaymentSummary($studentId) {
        try {
            // Get student's course fee
            $sql = "SELECT s.*, c.fee as course_fee
                    FROM students s
                    LEFT JOIN courses c ON s.course_id = c.id
                    WHERE s.id = ?";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([$studentId]);
            $student = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$student) {
                Response::notFound('Student not found');
                return;
            }

            $courseFee = $student['course_fee'] ?? 0;

            // Get total paid amount
            $sql = "SELECT COALESCE(SUM(amount), 0) as total_paid
                    FROM payments
                    WHERE student_id = ? AND status = 'paid'";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([$studentId]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $totalPaid = $result['total_paid'];

            // Calculate remaining balance
            $remainingBalance = $courseFee - $totalPaid;

            // Determine payment status
            $paymentStatus = 'pending';
            if ($courseFee <= 0) {
                $paymentStatus = 'pending'; // No course fee set
            } elseif ($remainingBalance <= 0) {
                $paymentStatus = 'paid'; // Fully paid
            } elseif ($totalPaid === 0) {
                $paymentStatus = 'pending'; // No payment made
            } else {
                $paymentStatus = 'partial'; // Partial payment made
            }

            // Check for overdue payments
            $sql = "SELECT COUNT(*) as overdue_count
                    FROM payments
                    WHERE student_id = ?
                    AND payment_due_date < CURDATE()
                    AND status IN ('pending', 'partial')";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([$studentId]);
            $overdueResult = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($overdueResult['overdue_count'] > 0 && $paymentStatus !== 'paid') {
                $paymentStatus = 'overdue';
            }

            $summary = [
                'totalPaid' => (float)$totalPaid,
                'courseFee' => (float)$courseFee,
                'remainingBalance' => (float)$remainingBalance,
                'paymentStatus' => $paymentStatus
            ];

            Response::success($summary, 'Student payment summary retrieved successfully');
        } catch (Exception $e) {
            Response::serverError('Failed to retrieve student payment summary: ' . $e->getMessage());
        }
    }

    public function recalculatePaymentStatus() {
        try {
            AuthMiddleware::requireAuth();

            $this->db->beginTransaction();

            // Get all students
            $sql = "SELECT id FROM students";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $students = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $updatedCount = 0;

            foreach ($students as $student) {
                // Get payment summary for each student
                $sql = "SELECT s.*, c.fee as course_fee
                        FROM students s
                        LEFT JOIN courses c ON s.course_id = c.id
                        WHERE s.id = ?";

                $stmt = $this->db->prepare($sql);
                $stmt->execute([$student['id']]);
                $studentData = $stmt->fetch(PDO::FETCH_ASSOC);

                if (!$studentData) continue;

                $courseFee = $studentData['course_fee'] ?? 0;

                // Get total paid amount
                $sql = "SELECT COALESCE(SUM(amount), 0) as total_paid
                        FROM payments
                        WHERE student_id = ? AND status = 'paid'";

                $stmt = $this->db->prepare($sql);
                $stmt->execute([$student['id']]);
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $totalPaid = $result['total_paid'];

                // Calculate payment status
                $paymentStatus = 'pending';
                if ($courseFee <= 0) {
                    $paymentStatus = 'pending';
                } elseif ($totalPaid >= $courseFee) {
                    $paymentStatus = 'paid';
                } elseif ($totalPaid === 0) {
                    $paymentStatus = 'pending';
                } else {
                    $paymentStatus = 'partial';
                }

                // Check for overdue payments
                $sql = "SELECT COUNT(*) as overdue_count
                        FROM payments
                        WHERE student_id = ?
                        AND payment_due_date < CURDATE()
                        AND status IN ('pending', 'partial')";

                $stmt = $this->db->prepare($sql);
                $stmt->execute([$student['id']]);
                $overdueResult = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($overdueResult['overdue_count'] > 0 && $paymentStatus !== 'paid') {
                    $paymentStatus = 'overdue';
                }

                // Update student payment status
                $sql = "UPDATE students SET payment_status = ?, updated_at = NOW() WHERE id = ?";
                $stmt = $this->db->prepare($sql);
                $stmt->execute([$paymentStatus, $student['id']]);
                $updatedCount++;
            }

            $this->db->commit();

            Response::success([
                'success' => true,
                'updatedCount' => $updatedCount
            ], 'Payment status recalculated successfully');
        } catch (Exception $e) {
            $this->db->rollBack();
            Response::serverError('Failed to recalculate payment status: ' . $e->getMessage());
        }
    }
}
?> 