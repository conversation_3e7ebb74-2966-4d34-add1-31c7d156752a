<?php
require_once __DIR__ . '/../models/Assignment.php';
require_once __DIR__ . '/../utils/Response.php';
require_once __DIR__ . '/../utils/Validator.php';
require_once __DIR__ . '/../middleware/AuthMiddleware.php';

class AssignmentController {
    private $db;
    private $assignmentModel;

    public function __construct($db) {
        $this->db = $db;
        $this->assignmentModel = new Assignment($db);
    }

    public function getAll() {
        AuthMiddleware::requireAuth();

        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 50;
        $offset = ($page - 1) * $limit;

        $courseId = $_GET['course_id'] ?? '';
        $levelId = $_GET['level_id'] ?? '';
        $status = $_GET['status'] ?? '';

        $assignments = $this->assignmentModel->getAll($limit, $offset, $courseId, $levelId, $status);
        $total = $this->assignmentModel->getTotalCount($courseId, $levelId, $status);

        Response::paginated($assignments, $total, $page, $limit, 'Assignments retrieved successfully');
    }

    public function getById($id) {
        AuthMiddleware::requireAuth();

        $assignment = $this->assignmentModel->getById($id);

        if ($assignment) {
            Response::success($assignment, 'Assignment retrieved successfully');
        } else {
            Response::notFound('Assignment not found');
        }
    }

    public function create() {
        AuthMiddleware::authorize(['super_admin', 'admin', 'teacher']);

        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            Response::error('Invalid JSON data', 400);
        }

        $validator = new Validator($data);
        $validator->required('title')
                 ->required('description')
                 ->required('course_id')
                 ->required('level_id')
                 ->required('due_date')
                 ->in('status', ['active', 'draft', 'completed', 'cancelled']);

        if ($validator->fails()) {
            Response::validationError($validator->getErrors());
        }

        // Sanitize input
        $assignmentData = [
            'title' => Validator::sanitizeInput($data['title']),
            'description' => Validator::sanitizeInput($data['description']),
            'course_id' => $data['course_id'],
            'level_id' => $data['level_id'],
            'due_date' => $data['due_date'],
            'status' => $data['status'] ?? 'active',
            'instructions' => isset($data['instructions']) ? Validator::sanitizeInput($data['instructions']) : null,
            'max_score' => isset($data['max_score']) ? floatval($data['max_score']) : null
        ];

        $assignmentId = $this->assignmentModel->create($assignmentData);

        if ($assignmentId) {
            $newAssignment = $this->assignmentModel->getById($assignmentId);
            Response::success($newAssignment, 'Assignment created successfully', 201);
        } else {
            Response::serverError('Failed to create assignment');
        }
    }

    public function update($id) {
        AuthMiddleware::authorize(['super_admin', 'admin', 'teacher']);

        // Check if assignment exists
        $assignment = $this->assignmentModel->getById($id);
        if (!$assignment) {
            Response::notFound('Assignment not found');
        }

        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            Response::error('Invalid JSON data', 400);
        }

        $validator = new Validator($data);
        
        if (isset($data['status'])) {
            $validator->in('status', ['active', 'draft', 'completed', 'cancelled']);
        }

        if ($validator->fails()) {
            Response::validationError($validator->getErrors());
        }

        // Sanitize input
        $updateData = [];
        $fields = ['title', 'description', 'course_id', 'level_id', 'due_date', 'status', 'instructions'];

        foreach ($fields as $field) {
            if (isset($data[$field])) {
                if (in_array($field, ['title', 'description', 'instructions'])) {
                    $updateData[$field] = Validator::sanitizeInput($data[$field]);
                } else {
                    $updateData[$field] = $data[$field];
                }
            }
        }

        if (isset($data['max_score'])) {
            $updateData['max_score'] = floatval($data['max_score']);
        }

        if (empty($updateData)) {
            Response::error('No valid fields to update', 400);
        }

        if ($this->assignmentModel->update($id, $updateData)) {
            $updatedAssignment = $this->assignmentModel->getById($id);
            Response::success($updatedAssignment, 'Assignment updated successfully');
        } else {
            Response::serverError('Failed to update assignment');
        }
    }

    public function delete($id) {
        AuthMiddleware::authorize(['super_admin', 'admin']);

        // Check if assignment exists
        $assignment = $this->assignmentModel->getById($id);
        if (!$assignment) {
            Response::notFound('Assignment not found');
        }

        if ($this->assignmentModel->delete($id)) {
            Response::success(null, 'Assignment deleted successfully');
        } else {
            Response::serverError('Failed to delete assignment');
        }
    }

    public function getStudentAssignments($studentId) {
        AuthMiddleware::requireAuth();

        // Get student to find their level
        require_once __DIR__ . '/../models/Student.php';
        $studentModel = new Student($this->db);
        $student = $studentModel->getById($studentId);

        if (!$student) {
            Response::notFound('Student not found');
            return;
        }

        if (!$student['level_id']) {
            Response::error('Student is not assigned to any level', 400);
            return;
        }

        $assignments = $this->assignmentModel->getByLevel($student['level_id']);
        Response::success($assignments, 'Student assignments retrieved successfully');
    }

    public function getLevelAssignments($levelId) {
        AuthMiddleware::requireAuth();

        $assignments = $this->assignmentModel->getByLevel($levelId);
        Response::success($assignments, 'Level assignments retrieved successfully');
    }
}
?>
