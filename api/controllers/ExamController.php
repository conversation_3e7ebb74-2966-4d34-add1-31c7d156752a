<?php
require_once __DIR__ . '/../models/Exam.php';
require_once __DIR__ . '/../utils/Response.php';
require_once __DIR__ . '/../utils/Validator.php';
require_once __DIR__ . '/../middleware/AuthMiddleware.php';

class ExamController {
    private $db;
    private $examModel;

    public function __construct($db) {
        $this->db = $db;
        $this->examModel = new Exam($db);
    }

    public function getAll() {
        AuthMiddleware::requireAuth();

        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 50;
        $offset = ($page - 1) * $limit;

        $courseId = $_GET['course_id'] ?? '';
        $levelId = $_GET['level_id'] ?? '';
        $status = $_GET['status'] ?? '';

        $exams = $this->examModel->getAll($limit, $offset, $courseId, $levelId, $status);
        $total = $this->examModel->getTotalCount($courseId, $levelId, $status);

        Response::paginated($exams, $total, $page, $limit, 'Exams retrieved successfully');
    }

    public function getById($id) {
        AuthMiddleware::requireAuth();

        $exam = $this->examModel->getById($id);

        if ($exam) {
            Response::success($exam, 'Exam retrieved successfully');
        } else {
            Response::notFound('Exam not found');
        }
    }

    public function create() {
        AuthMiddleware::authorize(['super_admin', 'admin', 'teacher']);

        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            Response::error('Invalid JSON data', 400);
        }

        $validator = new Validator($data);
        $validator->required('name')
                 ->required('subject')
                 ->required('course_id')
                 ->required('level_id')
                 ->required('type')
                 ->required('date')
                 ->required('time')
                 ->required('class')
                 ->in('type', ['midterm', 'final', 'quiz', 'assignment', 'practical'])
                 ->in('status', ['scheduled', 'in_progress', 'completed', 'cancelled']);

        if ($validator->fails()) {
            Response::validationError($validator->getErrors());
        }

        // Sanitize input
        $examData = [
            'name' => Validator::sanitizeInput($data['name']),
            'subject' => Validator::sanitizeInput($data['subject']),
            'course_id' => $data['course_id'],
            'level_id' => $data['level_id'],
            'type' => $data['type'],
            'date' => $data['date'],
            'time' => $data['time'],
            'class' => Validator::sanitizeInput($data['class']),
            'status' => $data['status'] ?? 'scheduled',
            'description' => isset($data['description']) ? Validator::sanitizeInput($data['description']) : null,
            'duration' => isset($data['duration']) ? intval($data['duration']) : null,
            'total_marks' => isset($data['total_marks']) ? floatval($data['total_marks']) : null
        ];

        $examId = $this->examModel->create($examData);

        if ($examId) {
            $newExam = $this->examModel->getById($examId);
            Response::success($newExam, 'Exam created successfully', 201);
        } else {
            Response::serverError('Failed to create exam');
        }
    }

    public function update($id) {
        AuthMiddleware::authorize(['super_admin', 'admin', 'teacher']);

        // Check if exam exists
        $exam = $this->examModel->getById($id);
        if (!$exam) {
            Response::notFound('Exam not found');
        }

        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            Response::error('Invalid JSON data', 400);
        }

        $validator = new Validator($data);
        
        if (isset($data['type'])) {
            $validator->in('type', ['midterm', 'final', 'quiz', 'assignment', 'practical']);
        }
        
        if (isset($data['status'])) {
            $validator->in('status', ['scheduled', 'in_progress', 'completed', 'cancelled']);
        }

        if ($validator->fails()) {
            Response::validationError($validator->getErrors());
        }

        // Sanitize input
        $updateData = [];
        $fields = ['name', 'subject', 'course_id', 'level_id', 'type', 'date', 'time', 'class', 'status', 'description'];

        foreach ($fields as $field) {
            if (isset($data[$field])) {
                if (in_array($field, ['name', 'subject', 'class', 'description'])) {
                    $updateData[$field] = Validator::sanitizeInput($data[$field]);
                } else {
                    $updateData[$field] = $data[$field];
                }
            }
        }

        if (isset($data['duration'])) {
            $updateData['duration'] = intval($data['duration']);
        }

        if (isset($data['total_marks'])) {
            $updateData['total_marks'] = floatval($data['total_marks']);
        }

        if (empty($updateData)) {
            Response::error('No valid fields to update', 400);
        }

        if ($this->examModel->update($id, $updateData)) {
            $updatedExam = $this->examModel->getById($id);
            Response::success($updatedExam, 'Exam updated successfully');
        } else {
            Response::serverError('Failed to update exam');
        }
    }

    public function delete($id) {
        AuthMiddleware::authorize(['super_admin', 'admin']);

        // Check if exam exists
        $exam = $this->examModel->getById($id);
        if (!$exam) {
            Response::notFound('Exam not found');
        }

        if ($this->examModel->delete($id)) {
            Response::success(null, 'Exam deleted successfully');
        } else {
            Response::serverError('Failed to delete exam');
        }
    }
}
?>
