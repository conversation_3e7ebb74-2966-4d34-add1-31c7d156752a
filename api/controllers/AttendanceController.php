<?php
require_once __DIR__ . '/../utils/Response.php';
require_once __DIR__ . '/../middleware/AuthMiddleware.php';

class AttendanceController {
    private $db;
    private $attendanceModel;

    public function __construct($db) {
        $this->db = $db;
        require_once __DIR__ . '/../models/Attendance.php';
        $this->attendanceModel = new Attendance($db);
    }

    public function getAll() {
        try {
            // Get query parameters
            $date = $_GET['date'] ?? null;
            $startDate = $_GET['start_date'] ?? null;
            $endDate = $_GET['end_date'] ?? null;
            $courseId = $_GET['course_id'] ?? null;
            $levelId = $_GET['level_id'] ?? null;
            $studentId = $_GET['student_id'] ?? null;

            if ($startDate && $endDate) {
                $records = $this->attendanceModel->getByDateRange($startDate, $endDate, $courseId, $levelId);
            } elseif ($date) {
                $records = $this->attendanceModel->getByDate($date, $courseId, $levelId);
            } elseif ($studentId) {
                $records = $this->attendanceModel->getByStudentId($studentId);
            } else {
                $records = $this->attendanceModel->getAll($courseId, $levelId);
            }

            Response::success($records, 'Attendance records retrieved successfully');
        } catch (Exception $e) {
            Response::serverError('Failed to retrieve attendance records: ' . $e->getMessage());
        }
    }

    public function getById($id) {
        try {
            $record = $this->attendanceModel->getById($id);
            if ($record) {
                Response::success($record, 'Attendance record retrieved successfully');
            } else {
                Response::notFound('Attendance record not found');
            }
        } catch (Exception $e) {
            Response::serverError('Failed to retrieve attendance record: ' . $e->getMessage());
        }
    }

    public function create() {
        try {
            AuthMiddleware::requireAuth();
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                Response::error('Invalid JSON input', 400);
                return;
            }

            // Validate required fields
            $required = ['student_id', 'date', 'status'];
            foreach ($required as $field) {
                if (!isset($input[$field]) || empty($input[$field])) {
                    Response::error("Field '$field' is required", 400);
                    return;
                }
            }

            $recordId = $this->attendanceModel->create($input);
            
            if ($recordId) {
                $record = $this->attendanceModel->getById($recordId);
                Response::success($record, 'Attendance record created successfully', 201);
            } else {
                Response::error('Failed to create attendance record', 500);
            }
        } catch (Exception $e) {
            Response::serverError('Failed to create attendance record: ' . $e->getMessage());
        }
    }

    public function update($id) {
        try {
            AuthMiddleware::requireAuth();
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                Response::error('Invalid JSON input', 400);
                return;
            }

            $success = $this->attendanceModel->update($id, $input);
            
            if ($success) {
                $record = $this->attendanceModel->getById($id);
                Response::success($record, 'Attendance record updated successfully');
            } else {
                Response::error('Failed to update attendance record', 500);
            }
        } catch (Exception $e) {
            Response::serverError('Failed to update attendance record: ' . $e->getMessage());
        }
    }

    public function delete($id) {
        try {
            AuthMiddleware::requireAuth();
            
            $success = $this->attendanceModel->delete($id);
            
            if ($success) {
                Response::success(null, 'Attendance record deleted successfully');
            } else {
                Response::error('Failed to delete attendance record', 500);
            }
        } catch (Exception $e) {
            Response::serverError('Failed to delete attendance record: ' . $e->getMessage());
        }
    }

    public function markAttendance() {
        try {
            AuthMiddleware::requireAuth();
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input || !isset($input['records']) || !is_array($input['records'])) {
                Response::error('Invalid input: records array is required', 400);
                return;
            }

            $results = [];
            $successCount = 0;
            $errorCount = 0;

            foreach ($input['records'] as $record) {
                try {
                    // Validate required fields for each record
                    $required = ['student_id', 'date', 'status'];
                    $valid = true;
                    foreach ($required as $field) {
                        if (!isset($record[$field]) || empty($record[$field])) {
                            $valid = false;
                            break;
                        }
                    }

                    if (!$valid) {
                        $results[] = [
                            'student_id' => $record['student_id'] ?? 'unknown',
                            'success' => false,
                            'error' => 'Missing required fields'
                        ];
                        $errorCount++;
                        continue;
                    }

                    // Check if record already exists
                    $existing = $this->attendanceModel->getByStudentAndDate($record['student_id'], $record['date']);
                    
                    if ($existing) {
                        // Update existing record
                        $success = $this->attendanceModel->update($existing['id'], $record);
                        $recordId = $existing['id'];
                    } else {
                        // Create new record
                        $recordId = $this->attendanceModel->create($record);
                        $success = $recordId !== false;
                    }

                    if ($success) {
                        $results[] = [
                            'student_id' => $record['student_id'],
                            'success' => true,
                            'record_id' => $recordId
                        ];
                        $successCount++;
                    } else {
                        $results[] = [
                            'student_id' => $record['student_id'],
                            'success' => false,
                            'error' => 'Failed to save record'
                        ];
                        $errorCount++;
                    }
                } catch (Exception $e) {
                    $results[] = [
                        'student_id' => $record['student_id'] ?? 'unknown',
                        'success' => false,
                        'error' => $e->getMessage()
                    ];
                    $errorCount++;
                }
            }

            $response = [
                'results' => $results,
                'summary' => [
                    'total' => count($input['records']),
                    'success' => $successCount,
                    'errors' => $errorCount
                ]
            ];

            if ($errorCount === 0) {
                Response::success($response, 'All attendance records marked successfully');
            } elseif ($successCount > 0) {
                Response::success($response, "Attendance marked: $successCount successful, $errorCount failed");
            } else {
                Response::error($response, 'Failed to mark attendance for all records', 400);
            }
        } catch (Exception $e) {
            Response::serverError('Failed to mark attendance: ' . $e->getMessage());
        }
    }

    public function getStudentAttendance($studentId) {
        try {
            $records = $this->attendanceModel->getByStudentId($studentId);
            Response::success($records, 'Student attendance retrieved successfully');
        } catch (Exception $e) {
            Response::serverError('Failed to retrieve student attendance: ' . $e->getMessage());
        }
    }
}
?>
