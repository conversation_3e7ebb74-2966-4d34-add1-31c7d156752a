<?php
require_once __DIR__ . '/../models/Event.php';
require_once __DIR__ . '/../utils/Response.php';
require_once __DIR__ . '/../utils/Validator.php';
require_once __DIR__ . '/../middleware/AuthMiddleware.php';

class EventController {
    private $db;
    private $eventModel;

    public function __construct($db) {
        $this->db = $db;
        $this->eventModel = new Event($db);
    }

    public function getAll() {
        AuthMiddleware::requireAuth();

        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 50;
        $offset = ($page - 1) * $limit;

        $category = $_GET['category'] ?? '';
        $startDate = $_GET['start_date'] ?? '';
        $endDate = $_GET['end_date'] ?? '';

        $events = $this->eventModel->getAll($limit, $offset, $category, $startDate, $endDate);
        $total = $this->eventModel->getTotalCount($category, $startDate, $endDate);

        Response::paginated($events, $total, $page, $limit, 'Events retrieved successfully');
    }

    public function getById($id) {
        AuthMiddleware::requireAuth();

        $event = $this->eventModel->getById($id);

        if ($event) {
            Response::success($event, 'Event retrieved successfully');
        } else {
            Response::notFound('Event not found');
        }
    }

    public function create() {
        AuthMiddleware::authorize(['super_admin', 'admin', 'teacher']);

        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            Response::error('Invalid JSON data', 400);
        }

        $validator = new Validator($data);
        $validator->required('title')
                 ->required('date')
                 ->required('category')
                 ->in('category', ['academic', 'social', 'sports', 'cultural', 'administrative', 'other']);

        if ($validator->fails()) {
            Response::validationError($validator->getErrors());
        }

        // Sanitize input
        $eventData = [
            'title' => Validator::sanitizeInput($data['title']),
            'description' => isset($data['description']) ? Validator::sanitizeInput($data['description']) : null,
            'date' => $data['date'],
            'time' => $data['time'] ?? null,
            'category' => $data['category'],
            'location' => isset($data['location']) ? Validator::sanitizeInput($data['location']) : null
        ];

        $eventId = $this->eventModel->create($eventData);

        if ($eventId) {
            $newEvent = $this->eventModel->getById($eventId);
            Response::success($newEvent, 'Event created successfully', 201);
        } else {
            Response::serverError('Failed to create event');
        }
    }

    public function update($id) {
        AuthMiddleware::authorize(['super_admin', 'admin', 'teacher']);

        // Check if event exists
        $event = $this->eventModel->getById($id);
        if (!$event) {
            Response::notFound('Event not found');
        }

        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            Response::error('Invalid JSON data', 400);
        }

        $validator = new Validator($data);
        
        if (isset($data['category'])) {
            $validator->in('category', ['academic', 'social', 'sports', 'cultural', 'administrative', 'other']);
        }

        if ($validator->fails()) {
            Response::validationError($validator->getErrors());
        }

        // Sanitize input
        $updateData = [];
        $fields = ['title', 'description', 'date', 'time', 'category', 'location'];

        foreach ($fields as $field) {
            if (isset($data[$field])) {
                if (in_array($field, ['title', 'description', 'location'])) {
                    $updateData[$field] = Validator::sanitizeInput($data[$field]);
                } else {
                    $updateData[$field] = $data[$field];
                }
            }
        }

        if (empty($updateData)) {
            Response::error('No valid fields to update', 400);
        }

        if ($this->eventModel->update($id, $updateData)) {
            $updatedEvent = $this->eventModel->getById($id);
            Response::success($updatedEvent, 'Event updated successfully');
        } else {
            Response::serverError('Failed to update event');
        }
    }

    public function delete($id) {
        AuthMiddleware::authorize(['super_admin', 'admin']);

        // Check if event exists
        $event = $this->eventModel->getById($id);
        if (!$event) {
            Response::notFound('Event not found');
        }

        if ($this->eventModel->delete($id)) {
            Response::success(null, 'Event deleted successfully');
        } else {
            Response::serverError('Failed to delete event');
        }
    }
}
?>
