# API Endpoints Documentation

This document lists all available API endpoints in the system.

## Base URL
All endpoints are prefixed with `/api/`

## Authentication
Most endpoints require authentication. Include the JWT token in the Authorization header:
```
Authorization: Bearer <token>
```

## Response Format
All responses follow this format:
```json
{
  "success": true|false,
  "message": "Response message",
  "data": {...},
  "pagination": {...} // For paginated responses
}
```

## Endpoints

### Authentication
- `POST /auth/login` - User login
- `POST /auth/register` - User registration
- `POST /auth/refresh` - Refresh JWT token
- `POST /auth/logout` - User logout
- `GET /auth/me` - Get current user info

### Users
- `GET /users` - Get all users (Admin only)
- `GET /users/{id}` - Get user by ID
- `POST /users` - Create new user (Admin only)
- `PUT /users/{id}` - Update user
- `DELETE /users/{id}` - Delete user (Admin only)

### Students
- `GET /students` - Get all students
- `GET /students/{id}` - Get student by ID
- `POST /students` - Create new student
- `PUT /students/{id}` - Update student
- `DELETE /students/{id}` - Delete student
- `GET /students/{id}/payments` - Get student payments
- `GET /students/{id}/transactions` - Get student transactions
- `GET /students/{id}/attendance` - Get student attendance
- `GET /students/{id}/payment-summary` - Get student payment summary
- `GET /students/{id}/assignments` - Get student assignments
- `POST /students/{id}/upload-photo` - Upload student photo

### Teachers
- `GET /teachers` - Get all teachers
- `GET /teachers/{id}` - Get teacher by ID
- `POST /teachers` - Create new teacher
- `PUT /teachers/{id}` - Update teacher
- `DELETE /teachers/{id}` - Delete teacher
- `GET /teachers/{id}/schedule` - Get teacher schedule
- `POST /teachers/{id}/courses` - Assign course to teacher
- `DELETE /teachers/{id}/courses/{courseId}` - Unassign course from teacher
- `POST /teachers/{id}/levels` - Assign level to teacher
- `DELETE /teachers/{id}/levels/{levelId}` - Unassign level from teacher

### Courses
- `GET /courses` - Get all courses
- `GET /courses/{id}` - Get course by ID
- `POST /courses` - Create new course
- `PUT /courses/{id}` - Update course
- `DELETE /courses/{id}` - Delete course
- `GET /courses/{id}/students` - Get course students

### Levels
- `GET /levels` - Get all levels
- `GET /levels/{id}` - Get level by ID
- `POST /levels` - Create new level
- `PUT /levels/{id}` - Update level
- `DELETE /levels/{id}` - Delete level
- `GET /levels/{id}/students` - Get level students
- `GET /levels/{id}/schedules` - Get level schedules
- `GET /levels/{id}/assignments` - Get level assignments

### Attendance
- `GET /attendance` - Get all attendance records
- `GET /attendance/{id}` - Get attendance record by ID
- `POST /attendance` - Create attendance record
- `PUT /attendance/{id}` - Update attendance record
- `DELETE /attendance/{id}` - Delete attendance record
- `POST /attendance/mark` - Bulk mark attendance

### Schedules
- `GET /schedules` - Get all schedules
- `GET /schedules/{id}` - Get schedule by ID
- `POST /schedules` - Create new schedule
- `PUT /schedules/{id}` - Update schedule
- `DELETE /schedules/{id}` - Delete schedule
- `GET /schedules/check-conflicts` - Check schedule conflicts

### Payments
- `GET /payments` - Get all payments
- `GET /payments/{id}` - Get payment by ID
- `POST /payments` - Create new payment
- `PUT /payments/{id}` - Update payment
- `DELETE /payments/{id}` - Delete payment
- `GET /payments/{id}/transactions` - Get payment transactions
- `POST /payments/recalculate-status` - Recalculate all payment statuses

### Transactions
- `GET /transactions` - Get all transactions
- `GET /transactions/{id}` - Get transaction by ID
- `POST /transactions` - Create new transaction
- `PUT /transactions/{id}` - Update transaction
- `DELETE /transactions/{id}` - Delete transaction

### Exams
- `GET /exams` - Get all exams
- `GET /exams/{id}` - Get exam by ID
- `POST /exams` - Create new exam
- `PUT /exams/{id}` - Update exam
- `DELETE /exams/{id}` - Delete exam

### Events
- `GET /events` - Get all events
- `GET /events/{id}` - Get event by ID
- `POST /events` - Create new event
- `PUT /events/{id}` - Update event
- `DELETE /events/{id}` - Delete event

### Materials
- `GET /materials` - Get all materials
- `GET /materials/{id}` - Get material by ID
- `POST /materials` - Create new material
- `PUT /materials/{id}` - Update material
- `DELETE /materials/{id}` - Delete material

### Announcements
- `GET /announcements` - Get all announcements
- `GET /announcements/{id}` - Get announcement by ID
- `POST /announcements` - Create new announcement
- `PUT /announcements/{id}` - Update announcement
- `DELETE /announcements/{id}` - Delete announcement

### Assignments
- `GET /assignments` - Get all assignments
- `GET /assignments/{id}` - Get assignment by ID
- `POST /assignments` - Create new assignment
- `PUT /assignments/{id}` - Update assignment
- `DELETE /assignments/{id}` - Delete assignment

## Query Parameters

### Pagination
Most GET endpoints support pagination:
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 50, max: 100)

### Filtering
Many endpoints support filtering:
- `search` - Search term
- `status` - Filter by status
- `course_id` - Filter by course
- `level_id` - Filter by level
- `start_date` - Filter by start date
- `end_date` - Filter by end date

### Sorting
Some endpoints support sorting:
- `sort_by` - Field to sort by
- `sort_direction` - `asc` or `desc`

## Error Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error
