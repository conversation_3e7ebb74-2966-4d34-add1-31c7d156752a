import { apiClient, User, LoginResponse } from './api-client';

// Auth service using PHP backend API
export class AuthService {
  // Sign in with email and password
  static async signIn(email: string, password: string): Promise<LoginResponse> {
    try {
      const response = await apiClient.login(email, password);
      return response;
    } catch (error: any) {
      console.error('Sign in error:', error);
      throw new Error(error.message || 'Failed to sign in');
    }
  }

  // Sign up with email and password
  static async signUp(name: string, email: string, password: string, role?: string): Promise<LoginResponse> {
    try {
      const response = await apiClient.register(name, email, password, role);
      return response;
    } catch (error: any) {
      console.error('Sign up error:', error);
      throw new Error(error.message || 'Failed to sign up');
    }
  }

  // Sign out
  static async signOut(): Promise<void> {
    try {
      await apiClient.logout();
    } catch (error: any) {
      console.error('Sign out error:', error);
      // Don't throw error for logout, just clear local storage
    }
  }

  // Get current user
  static async getCurrentUser(): Promise<User | null> {
    try {
      if (!apiClient.isAuthenticated()) {
        return null;
      }
      
      const user = await apiClient.getCurrentUser();
      return user;
    } catch (error: any) {
      console.error('Get current user error:', error);
      // If token is invalid, clear auth and return null
      apiClient.clearAuth();
      return null;
    }
  }

  // Get current user from storage (synchronous)
  static getCurrentUserFromStorage(): User | null {
    return apiClient.getCurrentUserFromStorage();
  }

  // Check if user is authenticated
  static isAuthenticated(): boolean {
    return apiClient.isAuthenticated();
  }

  // Refresh token
  static async refreshToken(): Promise<void> {
    try {
      await apiClient.refreshToken();
    } catch (error: any) {
      console.error('Refresh token error:', error);
      // If refresh fails, clear auth
      apiClient.clearAuth();
      throw error;
    }
  }

  // Clear authentication
  static clearAuth(): void {
    apiClient.clearAuth();
  }

  // Auth state change listener (simplified version)
  static onAuthStateChanged(callback: (user: User | null) => void): () => void {
    // Since we don't have real-time auth state changes like Firebase,
    // we'll check auth state periodically and on page focus
    let isActive = true;
    
    const checkAuthState = async () => {
      if (!isActive) return;
      
      try {
        const user = await AuthService.getCurrentUser();
        callback(user);
      } catch (error) {
        callback(null);
      }
    };

    // Initial check
    checkAuthState();

    // Check on page focus
    const handleFocus = () => {
      if (isActive) {
        checkAuthState();
      }
    };

    window.addEventListener('focus', handleFocus);

    // Periodic check every 5 minutes
    const interval = setInterval(() => {
      if (isActive) {
        checkAuthState();
      }
    }, 5 * 60 * 1000);

    // Return cleanup function
    return () => {
      isActive = false;
      window.removeEventListener('focus', handleFocus);
      clearInterval(interval);
    };
  }
}

// Export individual functions for compatibility with existing auth usage
export const signIn = AuthService.signIn;
export const signUp = AuthService.signUp;
export const signOut = AuthService.signOut;
export const getCurrentUser = AuthService.getCurrentUser;
export const onAuthStateChanged = AuthService.onAuthStateChanged;

// Export default
export default AuthService;
