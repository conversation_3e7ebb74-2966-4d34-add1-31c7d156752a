import { apiClient } from '@/lib/api-client';
import { format } from 'date-fns';

export interface ClassSchedule {
  id: string;
  level_id: string;
  course_name: string;
  room: string;
  start_time: string;
  end_time: string;
  day_of_week: number; // 0-6 (Sunday-Saturday)
  teacher_id: string;
  created_at: string;
  updated_at: string;
}

export const getTeacherSchedule = async (teacherId: string): Promise<ClassSchedule[]> => {
  try {
    console.log('Fetching teacher schedule for teacher:', teacherId);
    const schedules = await apiClient.get<ClassSchedule[]>(`/teachers/${teacherId}/schedule`);
    return schedules;
  } catch (error) {
    console.error('Error fetching teacher schedule:', error);
    return [];
  }
};

export const createSchedule = async (scheduleData: Omit<ClassSchedule, 'id' | 'created_at' | 'updated_at'>): Promise<ClassSchedule | null> => {
  try {
    console.log('Creating new schedule:', scheduleData);
    const newSchedule = await apiClient.post<ClassSchedule>('/schedules', scheduleData);
    return newSchedule;
  } catch (error) {
    console.error('Error creating schedule:', error);
    return null;
  }
};

export const updateSchedule = async (scheduleId: string, scheduleData: Partial<ClassSchedule>): Promise<boolean> => {
  try {
    console.log('Updating schedule:', scheduleId, scheduleData);
    await apiClient.put(`/schedules/${scheduleId}`, scheduleData);
    return true;
  } catch (error) {
    console.error('Error updating schedule:', error);
    return false;
  }
};

export const deleteSchedule = async (scheduleId: string): Promise<boolean> => {
  try {
    console.log('Deleting schedule:', scheduleId);
    await apiClient.delete(`/schedules/${scheduleId}`);
    return true;
  } catch (error) {
    console.error('Error deleting schedule:', error);
    return false;
  }
};

export const getSchedulesByLevel = async (levelId: string): Promise<ClassSchedule[]> => {
  try {
    console.log('Fetching schedules for level:', levelId);
    const schedules = await apiClient.get<ClassSchedule[]>(`/levels/${levelId}/schedules`);
    return schedules;
  } catch (error) {
    console.error('Error fetching level schedules:', error);
    return [];
  }
};

export const checkScheduleConflicts = async (
  teacherId: string,
  dayOfWeek: number,
  startTime: string,
  endTime: string,
  excludeScheduleId?: string
): Promise<boolean> => {
  try {
    console.log('Checking schedule conflicts for teacher:', teacherId, 'day:', dayOfWeek);

    // Get teacher's schedules for the specific day
    const params = new URLSearchParams();
    params.append('day_of_week', dayOfWeek.toString());
    if (excludeScheduleId) {
      params.append('exclude', excludeScheduleId);
    }

    const schedules = await apiClient.get<ClassSchedule[]>(`/teachers/${teacherId}/schedule?${params.toString()}`);

    // Check for time conflicts
    for (const schedule of schedules) {
      const newStart = parseInt(startTime.replace(':', ''));
      const newEnd = parseInt(endTime.replace(':', ''));
      const existingStart = parseInt(schedule.start_time.replace(':', ''));
      const existingEnd = parseInt(schedule.end_time.replace(':', ''));

      if (
        (newStart >= existingStart && newStart < existingEnd) ||
        (newEnd > existingStart && newEnd <= existingEnd) ||
        (newStart <= existingStart && newEnd >= existingEnd)
      ) {
        return true; // Conflict found
      }
    }

    return false; // No conflicts
  } catch (error) {
    console.error('Error checking schedule conflicts:', error);
    return true; // Return true to prevent scheduling in case of error
  }
};