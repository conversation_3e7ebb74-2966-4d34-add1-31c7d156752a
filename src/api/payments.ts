import { apiClient } from '@/lib/api-client';
import { getStudents, getStudent } from '@/api/students';
import { getCourses, getCourse } from '@/api/courses';
import { logActivity } from '../utils/activity-logger';
import { toast } from 'sonner';

export interface Payment {
  id: string;
  student_id: string;
  amount: number;
  date_paid: string;
  payment_due_date: string;
  status: 'paid' | 'pending' | 'overdue' | 'partial';
  notes?: string;
  created_at?: string;
  updated_at?: string;
  student?: {
    id: string;
    name: string;
    student_id: string;
  };
}

type PaymentInsert = {
  student_id: string;
  amount: number;
  date_paid: string;
  payment_due_date: string;
  status: 'paid' | 'pending' | 'overdue' | 'partial';
  notes?: string;
};

export const getPayments = async () => {
  try {
    console.log('Fetching payments from API');
    const payments = await apiClient.get<Payment[]>('/payments');
    return payments;
  } catch (error) {
    console.error('Error fetching payments:', error);
    throw error;
  }
};

export const getStudentPayments = async (studentId: string) => {
  try {
    console.log('Fetching student payments from API');
    const payments = await apiClient.get<Payment[]>(`/students/${studentId}/payments`);
    return payments;
  } catch (error) {
    console.error('Error fetching student payments:', error);
    throw error;
  }
};

export const getStudentPaymentSummary = async (studentId: string) => {
  try {
    console.log('Calculating student payment summary for student ID:', studentId);
    const summary = await apiClient.get(`/students/${studentId}/payment-summary`);
    return summary;
  } catch (error) {
    console.error('Error calculating student payment summary:', error);
    throw error;
  }
};

// Recalculate payment status for all students
export const recalculateAllStudentPaymentStatus = async () => {
  try {
    console.log('Recalculating payment status for all students');

    const result = await apiClient.post('/payments/recalculate-status');

    toast.success(`Successfully updated payment status for ${result.updatedCount} students`);
    return result;
  } catch (error: any) {
    console.error('Error recalculating payment status:', error);
    toast.error(error.message || 'Failed to recalculate payment status');
    throw error;
  }
};

export const createPayment = async (payment: PaymentInsert) => {
  try {
    console.log('Creating new payment:', payment);

    const newPayment = await apiClient.post<Payment>('/payments', payment);

    await logActivity('payment_created', {
      paymentId: newPayment.id,
      amount: payment.amount,
      studentId: payment.student_id
    });

    toast.success('Payment recorded successfully');
    return newPayment;
  } catch (error: any) {
    console.error('Error creating payment:', error);
    toast.error(error.message || 'Failed to record payment');
    throw error;
  }
};

export const updatePayment = async (paymentId: string, data: Partial<Payment>) => {
  try {
    console.log('Updating payment:', paymentId, data);

    await apiClient.put(`/payments/${paymentId}`, data);

    await logActivity('payment_updated', { paymentId });

    toast.success('Payment updated successfully');
    return true;
  } catch (error: any) {
    console.error('Error updating payment:', error);
    toast.error(error.message || 'Failed to update payment');
    throw error;
  }
};

export const deletePayment = async (paymentId: string) => {
  try {
    console.log('Deleting payment:', paymentId);

    await apiClient.delete(`/payments/${paymentId}`);

    await logActivity('payment_deleted', { paymentId });

    toast.success('Payment deleted successfully');
    return true;
  } catch (error: any) {
    console.error('Error deleting payment:', error);
    toast.error(error.message || 'Failed to delete payment');
    throw error;
  }
};
