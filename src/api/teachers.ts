import type { Teacher } from '@/types/user';
import { apiClient } from '@/lib/api-client';
import { toast } from 'sonner';
import { logActivity } from '@/utils/activity-logger';

interface GetTeachersOptions {
  id?: string;
  sortBy?: 'name' | 'email' | 'created_at';
  sortDirection?: 'asc' | 'desc';
  limit?: number;
  searchTerm?: string;
  courseId?: string;
  levelId?: string;
}

/**
 * Fetches teachers from API with various filter and sort options
 * @param options Optional parameters to filter and sort teachers
 * @returns Promise with an array of Teacher objects
 */
export const getTeachers = async (options: GetTeachersOptions = {}) => {
  try {
    console.log('Fetching teachers from API with options:', options);

    // If ID is provided, fetch single teacher
    if (options.id) {
      console.log(`Fetching single teacher with ID: ${options.id}`);

      try {
        const teacher = await apiClient.get<Teacher>(`/teachers/${options.id}`);
        return [teacher];
      } catch (error) {
        console.error(`Error fetching teacher with ID ${options.id}:`, error);
        return [];
      }
    }

    // Build query parameters
    const params = new URLSearchParams();

    if (options.searchTerm) {
      params.append('search', options.searchTerm);
    }
    if (options.courseId) {
      params.append('course_id', options.courseId);
    }
    if (options.levelId) {
      params.append('level_id', options.levelId);
    }
    if (options.sortBy) {
      params.append('sort_by', options.sortBy);
      params.append('sort_direction', options.sortDirection || 'asc');
    }
    if (options.limit) {
      params.append('limit', options.limit.toString());
    }

    const teachers = await apiClient.get<Teacher[]>(`/teachers?${params.toString()}`);
    console.log(`Fetched ${teachers.length} teachers from API`);
    return teachers;
  } catch (error) {
    console.error('Failed to fetch teachers from API:', error);
    return [];
  }
};



// Get a single teacher by ID - convenient helper function
export const getTeacherById = async (id: string): Promise<Teacher | null> => {
  try {
    const teacher = await apiClient.get<Teacher>(`/teachers/${id}`);
    return teacher;
  } catch (error) {
    console.error(`Error fetching teacher with ID ${id}:`, error);
    return null;
  }
};

// Get all teachers that are assigned to a specific course
export const getTeachersByCourse = async (courseId: string): Promise<Teacher[]> => {
  try {
    return await getTeachers({ courseId });
  } catch (error) {
    console.error(`Error fetching teachers for course ${courseId}:`, error);
    return [];
  }
};

// Get all teachers that are assigned to a specific level
export const getTeachersByLevel = async (levelId: string): Promise<Teacher[]> => {
  try {
    return await getTeachers({ levelId });
  } catch (error) {
    console.error(`Error fetching teachers for level ${levelId}:`, error);
    return [];
  }
};

export const createTeacher = async (teacherData: Omit<Teacher, 'id' | 'created_at' | 'updated_at'>) => {
  try {
    console.log('Creating teacher with data:', teacherData);

    const newTeacher = await apiClient.post<Teacher>('/teachers', teacherData);

    await logActivity('teacher_created', { teacherId: newTeacher.id });

    toast.success('Teacher created successfully');
    console.log('Teacher created successfully:', newTeacher);
    return newTeacher;
  } catch (error: any) {
    console.error('Error creating teacher:', error);

    if (error.message?.includes('email already exists')) {
      toast.error('This email address is already in use. Please use a different email.');
      throw new Error('This email address is already in use. Please use a different email.');
    }

    toast.error(error.message || 'Failed to create teacher');
    throw error;
  }
};

export const updateTeacher = async (teacherId: string, teacherData: Partial<Teacher>) => {
  try {
    console.log('Updating teacher:', teacherId, teacherData);

    await apiClient.put(`/teachers/${teacherId}`, teacherData);

    await logActivity('teacher_updated', { teacherId });

    toast.success('Teacher updated successfully');
    return true;
  } catch (error: any) {
    console.error('Error updating teacher:', error);
    toast.error(error.message || 'Failed to update teacher');
    throw error;
  }
};

export const deleteTeacher = async (id: string) => {
  try {
    const teacher = await getTeacherById(id);

    await apiClient.delete(`/teachers/${id}`);

    await logActivity('teacher_deleted', { teacherId: id });

    toast.success(`Successfully deleted teacher ${teacher?.name || id}`);
    return true;
  } catch (error: any) {
    console.error('Error deleting teacher:', error);
    toast.error(error.message || 'Failed to delete teacher');
    throw error;
  }
};

export const assignCourse = async (teacherId: string, courseId: string) => {
  try {
    console.log(`Assigning course ${courseId} to teacher ${teacherId}`);

    await apiClient.post(`/teachers/${teacherId}/courses`, { course_id: courseId });

    await logActivity('teacher_course_assigned', { teacherId, courseId });

    toast.success('Course assigned to teacher successfully');
    return true;
  } catch (error: any) {
    console.error('Error assigning course to teacher:', error);
    toast.error(error.message || 'Failed to assign course to teacher');
    throw error;
  }
};

export const unassignCourse = async (teacherId: string, courseId: string) => {
  try {
    console.log(`Unassigning course ${courseId} from teacher ${teacherId}`);

    await apiClient.delete(`/teachers/${teacherId}/courses/${courseId}`);

    await logActivity('teacher_course_unassigned', { teacherId, courseId });

    toast.success('Course unassigned from teacher successfully');
    return true;
  } catch (error: any) {
    console.error('Error unassigning course from teacher:', error);
    toast.error(error.message || 'Failed to unassign course from teacher');
    throw error;
  }
};

export const assignLevel = async (teacherId: string, levelId: string) => {
  try {
    console.log(`Assigning level ${levelId} to teacher ${teacherId}`);

    await apiClient.post(`/teachers/${teacherId}/levels`, { level_id: levelId });

    await logActivity('teacher_level_assigned', { teacherId, levelId });

    toast.success('Level assigned to teacher successfully');
    return true;
  } catch (error: any) {
    console.error('Error assigning level to teacher:', error);
    toast.error(error.message || 'Failed to assign level to teacher');
    throw error;
  }
};

export const unassignLevel = async (teacherId: string, levelId: string) => {
  try {
    console.log(`Unassigning level ${levelId} from teacher ${teacherId}`);

    await apiClient.delete(`/teachers/${teacherId}/levels/${levelId}`);

    await logActivity('teacher_level_unassigned', { teacherId, levelId });

    toast.success('Level unassigned from teacher successfully');
    return true;
  } catch (error: any) {
    console.error('Error unassigning level from teacher:', error);
    toast.error(error.message || 'Failed to unassign level from teacher');
    throw error;
  }
};

// Add a debugging function to directly check if a teacher exists
export const debugCheckTeacherExists = async (teacherId: string): Promise<boolean> => {
  try {
    console.log(`Debugging: Checking if teacher with ID ${teacherId} exists`);
    
    // First try to get the profile directly
    const profile = await getById<TeacherProfileDocument>('profiles', teacherId);
    
    if (profile) {
      console.log(`Found teacher profile:`, profile);
      return true;
    }
    
    // If not found directly, try querying by ID
    const profiles = await whereEqual<TeacherProfileDocument>('profiles', 'id', teacherId);
    
    if (profiles && profiles.length > 0) {
      console.log(`Found teacher profile through query:`, profiles[0]);
      return true;
    }
    
    // If still not found, check if any profile exists with this ID regardless of role
    const anyProfiles = await getById<ProfileDocument>('profiles', teacherId);
    
    if (anyProfiles) {
      console.log(`Found a profile with this ID but it's not a teacher:`, anyProfiles);
      return false;
    }
    
    console.log(`No profile found with ID ${teacherId}`);
    return false;
  } catch (error) {
    console.error(`Error checking if teacher exists:`, error);
    return false;
  }
};

// Add a debugging function to get all profiles regardless of role
export const debugGetAllProfiles = async (): Promise<ProfileDocument[]> => {
  try {
    console.log('Debugging: Getting all profiles from Firebase');
    
    try {
      // First try using the getAll helper
      const profiles = await getAll<ProfileDocument>('profiles');
      console.log(`Found ${profiles.length} total profiles using getAll`);
      return profiles;
    } catch (error: any) {
      console.error('Error using getAll for profiles:', error);
      
      // If we get an index error or any other error, fall back to direct Firestore query
      console.log('Falling back to direct Firestore query');
      
      try {
        // Use direct Firestore query as fallback
        const profilesRef = collection(db, 'profiles');
        const querySnapshot = await getDocs(query(profilesRef));
        
        const profiles: ProfileDocument[] = [];
        querySnapshot.forEach((doc) => {
          profiles.push({ id: doc.id, ...doc.data() } as ProfileDocument);
        });
        
        console.log(`Found ${profiles.length} total profiles using direct query`);
        return profiles;
      } catch (directError) {
        console.error('Error with direct Firestore query:', directError);
        throw directError; // Re-throw to be caught by outer catch
      }
    }
  } catch (error) {
    console.error('Error getting all profiles:', error);
    return [];
  }
};

// Add a new function to get a single profile by ID with direct Firestore access
export const debugGetProfileById = async (profileId: string): Promise<ProfileDocument | null> => {
  try {
    console.log(`Debugging: Getting profile with ID ${profileId} directly from Firestore`);
    
    // First try the standard method
    try {
      const profile = await getById<ProfileDocument>('profiles', profileId);
      if (profile) {
        console.log('Found profile using getById:', profile);
        return profile;
      }
    } catch (error) {
      console.error('Error using getById:', error);
    }
    
    // If that fails, try a direct Firestore query
    try {
      const profileDoc = await getDocs(query(
        collection(db, 'profiles'),
        whereEqual('id', profileId)
      ));
      
      if (!profileDoc.empty) {
        const profile = { id: profileDoc.docs[0].id, ...profileDoc.docs[0].data() } as ProfileDocument;
        console.log('Found profile using direct query:', profile);
        return profile;
      }
      
      console.log(`No profile found with ID ${profileId}`);
      return null;
    } catch (directError) {
      console.error('Error with direct Firestore query:', directError);
      return null;
    }
  } catch (error) {
    console.error(`Error getting profile with ID ${profileId}:`, error);
    return null;
  }
};

/**
 * Gets teachers without any sorting to avoid index requirements
 * This is a simpler version of getTeachers that doesn't require a composite index
 */
export const getTeachersWithoutSorting = async (): Promise<Teacher[]> => {
  try {
    console.log('Getting teachers without sorting (no index required)');
    
    // Just query for teachers without any sorting
    const constraints: QueryConstraint[] = [
      whereEqual('role', 'teacher')
    ];
    
    const profiles = await getAll<TeacherProfileDocument>('profiles', constraints);
    console.log(`Found ${profiles.length} teachers without sorting`);
    
    // Map Firebase profiles to our Teacher type
    return profiles.map(profile => ({
      id: profile.id,
      name: profile.displayName,
      email: profile.email,
      role: 'teacher' as const,
      assigned_courses: profile.assigned_courses || [],
      assigned_levels: profile.assigned_levels || [],
      specialization: profile.specialization || '',
      bio: profile.bio || '',
      contact_number: profile.contact_number || '',
      created_at: profile.created_at
    }));
  } catch (error) {
    console.error('Error getting teachers without sorting:', error);
    
    // If even this fails, try a direct Firestore query
    try {
      console.log('Falling back to direct Firestore query');
      const profilesRef = collection(db, 'profiles');
      const q = query(profilesRef, whereEqual('role', 'teacher'));
      const querySnapshot = await getDocs(q);
      
      const teachers: Teacher[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data() as TeacherProfileDocument;
        teachers.push({
          id: doc.id,
          name: data.displayName,
          email: data.email,
          role: 'teacher',
          assigned_courses: data.assigned_courses || [],
          assigned_levels: data.assigned_levels || [],
          specialization: data.specialization || '',
          bio: data.bio || '',
          contact_number: data.contact_number || '',
          created_at: data.created_at
        });
      });
      
      console.log(`Found ${teachers.length} teachers using direct query`);
      return teachers;
    } catch (directError) {
      console.error('Error with direct Firestore query:', directError);
      return [];
    }
  }
}; 