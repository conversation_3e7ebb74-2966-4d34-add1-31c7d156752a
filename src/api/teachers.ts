import type { Teacher } from '@/types/user';
import { apiClient } from '@/lib/api-client';
import { toast } from 'sonner';
import { logActivity } from '@/utils/activity-logger';

interface GetTeachersOptions {
  id?: string;
  sortBy?: 'name' | 'email' | 'created_at';
  sortDirection?: 'asc' | 'desc';
  limit?: number;
  searchTerm?: string;
  courseId?: string;
  levelId?: string;
}

/**
 * Fetches teachers from API with various filter and sort options
 * @param options Optional parameters to filter and sort teachers
 * @returns Promise with an array of Teacher objects
 */
export const getTeachers = async (options: GetTeachersOptions = {}) => {
  try {
    console.log('Fetching teachers from API with options:', options);

    // If ID is provided, fetch single teacher
    if (options.id) {
      console.log(`Fetching single teacher with ID: ${options.id}`);

      try {
        const teacher = await apiClient.get<Teacher>(`/teachers/${options.id}`);
        return [teacher];
      } catch (error) {
        console.error(`Error fetching teacher with ID ${options.id}:`, error);
        return [];
      }
    }

    // Build query parameters
    const params = new URLSearchParams();

    if (options.searchTerm) {
      params.append('search', options.searchTerm);
    }
    if (options.courseId) {
      params.append('course_id', options.courseId);
    }
    if (options.levelId) {
      params.append('level_id', options.levelId);
    }
    if (options.sortBy) {
      params.append('sort_by', options.sortBy);
      params.append('sort_direction', options.sortDirection || 'asc');
    }
    if (options.limit) {
      params.append('limit', options.limit.toString());
    }

    const teachers = await apiClient.get<Teacher[]>(`/teachers?${params.toString()}`);
    console.log(`Fetched ${teachers.length} teachers from API`);
    return teachers;
  } catch (error) {
    console.error('Failed to fetch teachers from API:', error);
    return [];
  }
};



// Get a single teacher by ID - convenient helper function
export const getTeacherById = async (id: string): Promise<Teacher | null> => {
  try {
    const teacher = await apiClient.get<Teacher>(`/teachers/${id}`);
    return teacher;
  } catch (error) {
    console.error(`Error fetching teacher with ID ${id}:`, error);
    return null;
  }
};

// Get all teachers that are assigned to a specific course
export const getTeachersByCourse = async (courseId: string): Promise<Teacher[]> => {
  try {
    return await getTeachers({ courseId });
  } catch (error) {
    console.error(`Error fetching teachers for course ${courseId}:`, error);
    return [];
  }
};

// Get all teachers that are assigned to a specific level
export const getTeachersByLevel = async (levelId: string): Promise<Teacher[]> => {
  try {
    return await getTeachers({ levelId });
  } catch (error) {
    console.error(`Error fetching teachers for level ${levelId}:`, error);
    return [];
  }
};

export const createTeacher = async (teacherData: Omit<Teacher, 'id' | 'created_at' | 'updated_at'>) => {
  try {
    console.log('Creating teacher with data:', teacherData);

    const newTeacher = await apiClient.post<Teacher>('/teachers', teacherData);

    await logActivity('student_created', { userId: newTeacher.id, role: 'teacher' });

    toast.success('Teacher created successfully');
    console.log('Teacher created successfully:', newTeacher);
    return newTeacher;
  } catch (error: any) {
    console.error('Error creating teacher:', error);

    if (error.message?.includes('email already exists')) {
      toast.error('This email address is already in use. Please use a different email.');
      throw new Error('This email address is already in use. Please use a different email.');
    }

    toast.error(error.message || 'Failed to create teacher');
    throw error;
  }
};

export const updateTeacher = async (teacherId: string, teacherData: Partial<Teacher>) => {
  try {
    console.log('Updating teacher:', teacherId, teacherData);

    await apiClient.put(`/teachers/${teacherId}`, teacherData);

    await logActivity('student_updated', { userId: teacherId, role: 'teacher' });

    toast.success('Teacher updated successfully');
    return true;
  } catch (error: any) {
    console.error('Error updating teacher:', error);
    toast.error(error.message || 'Failed to update teacher');
    throw error;
  }
};

export const deleteTeacher = async (id: string) => {
  try {
    const teacher = await getTeacherById(id);

    await apiClient.delete(`/teachers/${id}`);

    await logActivity('student_deleted', { userId: id, role: 'teacher' });

    toast.success(`Successfully deleted teacher ${teacher?.name || id}`);
    return true;
  } catch (error: any) {
    console.error('Error deleting teacher:', error);
    toast.error(error.message || 'Failed to delete teacher');
    throw error;
  }
};

export const assignCourse = async (teacherId: string, courseId: string) => {
  try {
    console.log(`Assigning course ${courseId} to teacher ${teacherId}`);

    await apiClient.post(`/teachers/${teacherId}/courses`, { course_id: courseId });

    await logActivity('course_updated', { teacherId, courseId, action: 'assigned' });

    toast.success('Course assigned to teacher successfully');
    return true;
  } catch (error: any) {
    console.error('Error assigning course to teacher:', error);
    toast.error(error.message || 'Failed to assign course to teacher');
    throw error;
  }
};

export const unassignCourse = async (teacherId: string, courseId: string) => {
  try {
    console.log(`Unassigning course ${courseId} from teacher ${teacherId}`);

    await apiClient.delete(`/teachers/${teacherId}/courses/${courseId}`);

    await logActivity('course_updated', { teacherId, courseId, action: 'unassigned' });

    toast.success('Course unassigned from teacher successfully');
    return true;
  } catch (error: any) {
    console.error('Error unassigning course from teacher:', error);
    toast.error(error.message || 'Failed to unassign course from teacher');
    throw error;
  }
};

export const assignLevel = async (teacherId: string, levelId: string) => {
  try {
    console.log(`Assigning level ${levelId} to teacher ${teacherId}`);

    await apiClient.post(`/teachers/${teacherId}/levels`, { level_id: levelId });

    await logActivity('level_updated', { teacherId, levelId, action: 'assigned' });

    toast.success('Level assigned to teacher successfully');
    return true;
  } catch (error: any) {
    console.error('Error assigning level to teacher:', error);
    toast.error(error.message || 'Failed to assign level to teacher');
    throw error;
  }
};

export const unassignLevel = async (teacherId: string, levelId: string) => {
  try {
    console.log(`Unassigning level ${levelId} from teacher ${teacherId}`);

    await apiClient.delete(`/teachers/${teacherId}/levels/${levelId}`);

    await logActivity('level_updated', { teacherId, levelId, action: 'unassigned' });

    toast.success('Level unassigned from teacher successfully');
    return true;
  } catch (error: any) {
    console.error('Error unassigning level from teacher:', error);
    toast.error(error.message || 'Failed to unassign level from teacher');
    throw error;
  }
};

// Simplified function to check if a teacher exists
export const checkTeacherExists = async (teacherId: string): Promise<boolean> => {
  try {
    const teacher = await getTeacherById(teacherId);
    return teacher !== null;
  } catch (error) {
    console.error(`Error checking if teacher exists:`, error);
    return false;
  }
};





