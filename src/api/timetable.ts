import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/lib/api-client';

// Timetable types - to be defined based on PHP backend structure
interface ClassTimetableEntry {
  id: string;
  subject: string;
  time: string;
  day: string;
  room?: string;
  class_id?: string;
}

interface ExamTimetableEntry {
  id: string;
  subject: string;
  date: string;
  time: string;
  duration: string;
  room?: string;
}

// Query keys
const CLASS_TIMETABLE_KEY = 'class-timetable';
const EXAM_TIMETABLE_KEY = 'exam-timetable';
const CLASS_TIMETABLE_BY_LEVEL_KEY = 'class-timetable-by-level';
const EXAM_TIMETABLE_BY_LEVEL_KEY = 'exam-timetable-by-level';
const CLASS_TIMETABLE_BY_DAY_KEY = 'class-timetable-by-day';
const CLASS_TIMETABLE_BY_TEACHER_KEY = 'class-timetable-by-teacher';
const EXAM_TIMETABLE_BY_DATE_RANGE_KEY = 'exam-timetable-by-date-range';
const EXAM_TIMETABLE_BY_TYPE_KEY = 'exam-timetable-by-type';

// Class Timetable Hooks

// Get all class timetable entries
export const useClassTimetable = () => {
  return useQuery({
    queryKey: [CLASS_TIMETABLE_KEY],
    queryFn: () => apiClient.get<ClassTimetableEntry[]>('/schedules'),
  });
};

// Get class timetable entries by level
export const useClassTimetableByLevel = (levelId: string) => {
  return useQuery({
    queryKey: [CLASS_TIMETABLE_BY_LEVEL_KEY, levelId],
    queryFn: () => apiClient.get<ClassTimetableEntry[]>(`/levels/${levelId}/schedules`),
    enabled: !!levelId,
  });
};

// Get class timetable entries by day
export const useClassTimetableByDay = (day: string) => {
  return useQuery({
    queryKey: [CLASS_TIMETABLE_BY_DAY_KEY, day],
    queryFn: () => apiClient.get<ClassTimetableEntry[]>(`/schedules?day=${day}`),
    enabled: !!day,
  });
};

// Get class timetable entries by teacher
export const useClassTimetableByTeacher = (teacherId: string) => {
  return useQuery({
    queryKey: [CLASS_TIMETABLE_BY_TEACHER_KEY, teacherId],
    queryFn: () => apiClient.get<ClassTimetableEntry[]>(`/teachers/${teacherId}/schedule`),
    enabled: !!teacherId,
  });
};

// Create a new class timetable entry
export const useCreateClassTimetableEntry = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (entry: Omit<ClassTimetableEntry, 'id'>) =>
      apiClient.post<ClassTimetableEntry>('/schedules', entry),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CLASS_TIMETABLE_KEY] });
    },
  });
};

// Update a class timetable entry
export const useUpdateClassTimetableEntry = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, entry }: { id: string; entry: Partial<ClassTimetableEntry> }) =>
      apiClient.put<ClassTimetableEntry>(`/schedules/${id}`, entry),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CLASS_TIMETABLE_KEY] });
    },
  });
};

// Delete a class timetable entry
export const useDeleteClassTimetableEntry = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => apiClient.delete(`/schedules/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CLASS_TIMETABLE_KEY] });
    },
  });
};

// Exam Timetable Hooks

// Get all exam timetable entries
export const useExamTimetable = () => {
  return useQuery({
    queryKey: [EXAM_TIMETABLE_KEY],
    queryFn: () => apiClient.get<ExamTimetableEntry[]>('/exams'),
  });
};

// Get exam timetable entries by level
export const useExamTimetableByLevel = (levelId: string) => {
  return useQuery({
    queryKey: [EXAM_TIMETABLE_BY_LEVEL_KEY, levelId],
    queryFn: () => apiClient.get<ExamTimetableEntry[]>(`/exams?level_id=${levelId}`),
    enabled: !!levelId,
  });
};

// Get exam timetable entries by date range
export const useExamTimetableByDateRange = (startDate: string, endDate: string) => {
  return useQuery({
    queryKey: [EXAM_TIMETABLE_BY_DATE_RANGE_KEY, startDate, endDate],
    queryFn: () => apiClient.get<ExamTimetableEntry[]>(`/exams?start_date=${startDate}&end_date=${endDate}`),
    enabled: !!startDate && !!endDate,
  });
};

// Get exam timetable entries by exam type
export const useExamTimetableByType = (examType: string) => {
  return useQuery({
    queryKey: [EXAM_TIMETABLE_BY_TYPE_KEY, examType],
    queryFn: () => apiClient.get<ExamTimetableEntry[]>(`/exams?type=${examType}`),
    enabled: !!examType,
  });
};

// Create a new exam timetable entry
export const useCreateExamTimetableEntry = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (entry: Omit<ExamTimetableEntry, 'id'>) =>
      apiClient.post<ExamTimetableEntry>('/exams', entry),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [EXAM_TIMETABLE_KEY] });
    },
  });
};

// Update an exam timetable entry
export const useUpdateExamTimetableEntry = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, entry }: { id: string; entry: Partial<ExamTimetableEntry> }) =>
      apiClient.put<ExamTimetableEntry>(`/exams/${id}`, entry),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [EXAM_TIMETABLE_KEY] });
    },
  });
};

// Delete an exam timetable entry
export const useDeleteExamTimetableEntry = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => apiClient.delete(`/exams/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [EXAM_TIMETABLE_KEY] });
    },
  });
};

// Direct API functions for components that don't use React Query hooks

// Class Timetable Functions

// Get all class timetable entries
export const getAllClassTimetable = async (): Promise<ClassTimetableEntry[]> => {
  return await apiClient.get<ClassTimetableEntry[]>('/schedules');
};

// Get class timetable entries by level
export const getClassTimetableByLevel = async (levelId: string): Promise<ClassTimetableEntry[]> => {
  return await apiClient.get<ClassTimetableEntry[]>(`/levels/${levelId}/schedules`);
};

// Get class timetable entries by day
export const getClassTimetableByDay = async (day: string): Promise<ClassTimetableEntry[]> => {
  return await apiClient.get<ClassTimetableEntry[]>(`/schedules?day=${day}`);
};

// Get class timetable entries by teacher
export const getClassTimetableByTeacher = async (teacherId: string): Promise<ClassTimetableEntry[]> => {
  return await apiClient.get<ClassTimetableEntry[]>(`/teachers/${teacherId}/schedule`);
};

// Create a new class timetable entry
export const createClassTimetableEntry = async (entry: Omit<ClassTimetableEntry, 'id'>): Promise<ClassTimetableEntry> => {
  return await apiClient.post<ClassTimetableEntry>('/schedules', entry);
};

// Update a class timetable entry
export const updateClassTimetableEntry = async (id: string, entry: Partial<ClassTimetableEntry>): Promise<ClassTimetableEntry> => {
  return await apiClient.put<ClassTimetableEntry>(`/schedules/${id}`, entry);
};

// Delete a class timetable entry
export const deleteClassTimetableEntry = async (id: string): Promise<void> => {
  await apiClient.delete(`/schedules/${id}`);
};

// Exam Timetable Functions

// Get all exam timetable entries
export const getAllExamTimetable = async (): Promise<ExamTimetableEntry[]> => {
  return await apiClient.get<ExamTimetableEntry[]>('/exams');
};

// Get exam timetable entries by level
export const getExamTimetableByLevel = async (levelId: string): Promise<ExamTimetableEntry[]> => {
  return await apiClient.get<ExamTimetableEntry[]>(`/exams?level_id=${levelId}`);
};

// Get exam timetable entries by date range
export const getExamTimetableByDateRange = async (startDate: string, endDate: string): Promise<ExamTimetableEntry[]> => {
  return await apiClient.get<ExamTimetableEntry[]>(`/exams?start_date=${startDate}&end_date=${endDate}`);
};

// Get exam timetable entries by exam type
export const getExamTimetableByType = async (examType: string): Promise<ExamTimetableEntry[]> => {
  return await apiClient.get<ExamTimetableEntry[]>(`/exams?type=${examType}`);
};

// Create a new exam timetable entry
export const createExamTimetableEntry = async (entry: Omit<ExamTimetableEntry, 'id'>): Promise<ExamTimetableEntry> => {
  return await apiClient.post<ExamTimetableEntry>('/exams', entry);
};

// Update an exam timetable entry
export const updateExamTimetableEntry = async (id: string, entry: Partial<ExamTimetableEntry>): Promise<ExamTimetableEntry> => {
  return await apiClient.put<ExamTimetableEntry>(`/exams/${id}`, entry);
};

// Delete an exam timetable entry
export const deleteExamTimetableEntry = async (id: string): Promise<void> => {
  await apiClient.delete(`/exams/${id}`);
};