import { apiClient } from '@/lib/api-client';
import { toast } from 'sonner';
import { logActivity } from '@/utils/activity-logger';

export interface AttendanceRecord {
  id: string;
  student_id: string;
  date: string;
  status: 'present' | 'absent' | 'late' | 'excused';
  notes?: string;
  marked_by?: string;
  course_id?: string;
  level_id?: string;
  created_at?: string;
  updated_at?: string;
  student_name?: string;
  student?: any;
}

// For single date attendance records
export const getAttendanceByDate = async (levelId: string, date: string) => {
  try {
    console.log('Fetching attendance records for level:', levelId, 'date:', date);
    const params = new URLSearchParams();
    params.append('date', date);
    if (levelId) {
      params.append('level_id', levelId);
    }

    const records = await apiClient.get<AttendanceRecord[]>(`/attendance?${params.toString()}`);
    return records;
  } catch (error) {
    console.error('Error fetching attendance records by date:', error);
    throw error;
  }
};

// For date range attendance records
export const getAttendanceByDateRange = async (levelId: string, startDate: string, endDate: string) => {
  try {
    console.log('Fetching attendance records for level:', levelId, 'from:', startDate, 'to:', endDate);
    const params = new URLSearchParams();
    params.append('start_date', startDate);
    params.append('end_date', endDate);
    if (levelId) {
      params.append('level_id', levelId);
    }

    const records = await apiClient.get<AttendanceRecord[]>(`/attendance?${params.toString()}`);
    return records;
  } catch (error) {
    console.error('Error fetching attendance records by date range:', error);
    throw error;
  }
};

export const getAttendanceRecords = async (date?: string, courseId?: string, levelId?: string) => {
  try {
    console.log('Fetching attendance records with filters:', { date, courseId, levelId });
    const params = new URLSearchParams();

    if (date) {
      params.append('date', date);
    }
    if (courseId) {
      params.append('course_id', courseId);
    }
    if (levelId) {
      params.append('level_id', levelId);
    }

    const records = await apiClient.get<AttendanceRecord[]>(`/attendance?${params.toString()}`);
    return records;
  } catch (error) {
    console.error('Error fetching attendance records:', error);
    throw error;
  }
};

export const getStudentAttendance = async (studentId: string) => {
  try {
    console.log('Fetching student attendance for student ID:', studentId);
    const records = await apiClient.get<AttendanceRecord[]>(`/students/${studentId}/attendance`);

    // Sort records by date (newest first)
    const sortedRecords = [...records].sort((a, b) => {
      return new Date(b.date).getTime() - new Date(a.date).getTime();
    });

    console.log(`Found ${sortedRecords.length} attendance records for student ${studentId}`);
    return sortedRecords;
  } catch (error) {
    console.error('Error fetching student attendance:', error);
    throw error;
  }
};

// New markAttendance function that matches the signature used in AttendanceManagement component
export const markAttendance = async (data: {
  level_id: string;
  date: string;
  attendance: { student_id: string; present: boolean }[];
}) => {
  try {
    console.log(`Marking attendance for level ${data.level_id} on ${data.date} for ${data.attendance.length} students`);

    // Prepare attendance records for bulk creation/update
    const attendanceRecords = data.attendance.map(record => ({
      student_id: record.student_id,
      date: data.date,
      status: record.present ? 'present' : 'absent',
      level_id: data.level_id
    }));

    // Use the bulk mark attendance API endpoint
    const result = await apiClient.post('/attendance/mark', {
      level_id: data.level_id,
      date: data.date,
      records: attendanceRecords
    });

    await logActivity('attendance_bulk_created', {
      count: data.attendance.length,
      date: data.date,
      level_id: data.level_id
    });

    toast.success(`Attendance marked for ${data.attendance.length} students`);
    return result;
  } catch (error: any) {
    console.error('Error marking attendance:', error);
    toast.error(error.message || 'Failed to mark attendance');
    throw error;
  }
};

// Original markAttendance function for individual students
export const markStudentAttendance = async (
  studentId: string,
  date: string,
  status: 'present' | 'absent' | 'late' | 'excused',
  notes?: string
) => {
  try {
    console.log(`Marking attendance for student ${studentId} on ${date} as ${status}`);

    // Create or update attendance record via API
    const attendanceData = {
      student_id: studentId,
      date,
      status,
      notes: notes || ''
    };

    // Try to get existing record first by calling the API with query parameters
    const allRecords = await getAttendanceRecords();
    const existingRecords = allRecords.filter(record =>
      record.student_id === studentId && record.date === date
    );

    let result;
    if (existingRecords.length > 0) {
      // Update existing record
      console.log(`Updating existing attendance record for student ${studentId}`);
      result = await apiClient.put(`/attendance/${existingRecords[0].id}`, attendanceData);

      await logActivity('attendance_updated', {
        id: existingRecords[0].id,
        studentId,
        date,
        status
      });
    } else {
      // Create new record
      console.log(`Creating new attendance record for student ${studentId}`);
      result = await apiClient.post('/attendance', attendanceData);

      await logActivity('attendance_created', {
        id: (result as any).id,
        studentId,
        date,
        status
      });
    }

    toast.success('Attendance updated successfully');
    return result;
  } catch (error: any) {
    console.error('Error marking attendance:', error);
    toast.error(error.message || 'Failed to mark attendance');
    throw error;
  }
};

export const bulkMarkAttendance = async (
  studentIds: string[],
  date: string,
  status: 'present' | 'absent' | 'late' | 'excused'
) => {
  try {
    console.log(`Bulk marking attendance for ${studentIds.length} students on ${date} as ${status}`);

    // Prepare attendance records for bulk creation/update
    const attendanceRecords = studentIds.map(studentId => ({
      student_id: studentId,
      date: date,
      status: status,
      notes: ''
    }));

    // Use the bulk mark attendance API endpoint
    const result = await apiClient.post('/attendance/mark', {
      date: date,
      records: attendanceRecords
    });

    await logActivity('attendance_bulk_created', {
      count: studentIds.length,
      date,
      status
    });

    toast.success(`Marked attendance for ${studentIds.length} students`);
    return result;
  } catch (error: any) {
    console.error('Error bulk marking attendance:', error);
    toast.error(error.message || 'Failed to mark attendance');
    throw error;
  }
};