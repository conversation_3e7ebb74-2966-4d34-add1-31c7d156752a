import { useQuery } from '@tanstack/react-query';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  Legend
} from 'recharts';
import { Loader2, AlertCircle, Users, DollarSign, Calendar, Award } from 'lucide-react';
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useAuth } from '@/contexts/AuthContext';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { apiClient } from '@/lib/api-client';
import { format } from 'date-fns';
import { getStudents } from '@/api/students';
import { getTransactions } from '@/api/transactions';
import { getExams } from '@/api/exams';
import { getAttendanceRecords } from '@/api/attendance';

// Define types for our data
interface Student {
  id: string;
  name: string;
  student_id?: string;
  [key: string]: any;
}

interface Transaction {
  id: string;
  amount: number;
  type: 'income' | 'expense';
  date: string;
  description: string;
  category: string;
  status: string;
  [key: string]: any;
}

interface ExamResult {
  id: string;
  marks: number;
  student_id: string;
  grade: string;
  student?: Student;
  [key: string]: any;
}

interface Exam {
  id: string;
  name: string;
  date: string;
  time: string;
  subject: string;
  [key: string]: any;
}

interface Attendance {
  id: string;
  date: string;
  student_id: string;
  status: string;
  [key: string]: any;
}

interface DashboardStats {
  totalStudents: number;
  totalRevenue: number;
  topPerformers: ExamResult[];
  upcomingExams: Exam[];
  todayAttendance: number;
  attendancePercentage: number;
  monthlyData: { month: string; earnings: number; expenses: number }[];
  attendanceData: { name: string; value: number }[];
}

export const DashboardOverview = () => {
  const { user } = useAuth();

  const { data: stats, isLoading: statsLoading, error: statsError } = useQuery<DashboardStats>({
    queryKey: ['dashboard-stats'],
    queryFn: async () => {
      if (!user) throw new Error('No active user');

      try {
        console.log('Fetching dashboard data...');
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const todayStr = format(today, 'yyyy-MM-dd');

        // Get students
        console.log('Fetching students...');
        const students = await getStudents();
        console.log(`Found ${students.length} students`);

        // Get transactions
        console.log('Fetching transactions...');
        const transactions = await getTransactions() as Transaction[];
        console.log(`Found ${transactions.length} transactions`);

        // Calculate monthly data for current year
        const currentYear = new Date().getFullYear();
        const filteredTransactions = transactions.filter((t: Transaction) => {
          const transactionDate = new Date(t.date);
          return transactionDate.getFullYear() === currentYear;
        });

        const monthlyData = Array.from({ length: 12 }, (_, i) => ({
          month: new Date(currentYear, i, 1).toLocaleString('default', { month: 'short' }),
          earnings: 0,
          expenses: 0
        }));

        filteredTransactions.forEach((transaction: Transaction) => {
          const transactionDate = new Date(transaction.date);
          const monthIndex = transactionDate.getMonth();

          if (transaction.type === 'income') {
            monthlyData[monthIndex].earnings += Number(transaction.amount);
          } else if (transaction.type === 'expense') {
            monthlyData[monthIndex].expenses += Number(transaction.amount);
          }
        });

        // Get exam results
        console.log('Fetching exam results...');
        const examResultsData = await apiClient.get('/exam-results') as any[];

        const examResults = examResultsData.map(data => {
          return {
            id: data.id,
            ...data,
            // Ensure we have a creation date for sorting by recency
            created_at: data.created_at || data.date_recorded || new Date().toISOString(),
            // Ensure required fields are present to satisfy TypeScript
            marks: data.marks || 0,
            student_id: data.student_id || '',
            grade: data.grade || '',
            exam_id: data.exam_id || ''
          } as ExamResult;
        }).sort((a, b) => b.marks - a.marks); // Sort by marks descending
        
        // Process exam results to ensure each student appears only once with their best/latest score
        const studentMap = new Map<string, ExamResult>();
        
        // Group results by student_id and keep only the highest score or most recent one
        examResults.forEach(result => {
          if (!result.student_id) return;
          
          const existingResult = studentMap.get(result.student_id);
          
          // If no existing result for this student, add this one
          if (!existingResult) {
            studentMap.set(result.student_id, result);
            return;
          }
          
          // If this is for the same exam and it's newer, update it
          if (existingResult.exam_id === result.exam_id) {
            // Compare dates - newer results override older ones
            const existingDate = new Date(existingResult.created_at);
            const newDate = new Date(result.created_at);
            
            if (newDate > existingDate) {
              studentMap.set(result.student_id, result);
            }
            return;
          }
          
          // Otherwise, keep the result with the higher score
          if (result.marks > existingResult.marks) {
            studentMap.set(result.student_id, result);
          }
        });
        
        // Convert map back to array and take top 5
        const uniqueResults = Array.from(studentMap.values())
          .sort((a, b) => b.marks - a.marks)
          .slice(0, 5);
        
        // Get student details for each exam result
        const topPerformers = uniqueResults.map((result) => {
          if (result.student_id) {
            console.log(`Finding student details for student ID: ${result.student_id}`);

            // Find student by student_id or id
            const student = students.find(s => s.student_id === result.student_id || s.id === result.student_id);

            if (student) {
              result.student = student;
              console.log(`Found student: ${student.name || 'Unknown'}`);
            } else {
              console.warn(`No student found for exam result with student_id: ${result.student_id}`);
            }
          }
          return result;
        });
        console.log(`Found ${topPerformers.length} top performers`);

        // Get upcoming exams
        console.log('Fetching upcoming exams...');
        const allExams = await getExams() as Exam[];

        const upcomingExams = allExams
          .filter((exam: Exam) => new Date(exam.date) >= today)
          .sort((a: Exam, b: Exam) => new Date(a.date).getTime() - new Date(b.date).getTime())
          .slice(0, 3);
        
        console.log(`Found ${upcomingExams.length} upcoming exams`);

        // Get attendance records for today
        console.log('Fetching attendance records...');
        const attendanceRecords = await getAttendanceRecords(todayStr) as Attendance[];
        
        console.log(`Found ${attendanceRecords.length} attendance records for today`);

        // Calculate attendance data for pie chart
        const presentStudents = attendanceRecords.filter(record => record.status === 'present').length;
        const absentStudents = attendanceRecords.filter(record => record.status === 'absent').length;
        const lateStudents = attendanceRecords.filter(record => record.status === 'late').length;
        const excusedStudents = attendanceRecords.filter(record => record.status === 'excused').length;
        
        const attendanceData = [
          { name: 'Present', value: presentStudents },
          { name: 'Absent', value: absentStudents },
          { name: 'Late', value: lateStudents },
          { name: 'Excused', value: excusedStudents },
          { name: 'Not Marked', value: Math.max(0, students.length - attendanceRecords.length) }
        ].filter(item => item.value > 0);

        // Calculate total revenue
        const totalIncome = transactions
          .filter(t => t.type === 'income')
          .reduce((sum, t) => sum + (Number(t.amount) || 0), 0);
        
        const totalExpense = transactions
          .filter(t => t.type === 'expense')
          .reduce((sum, t) => sum + (Number(t.amount) || 0), 0);
        
        const totalRevenue = totalIncome - totalExpense;

        return {
          totalStudents: students.length,
          totalRevenue,
          topPerformers,
          upcomingExams,
          todayAttendance: presentStudents + lateStudents + excusedStudents,
          attendancePercentage: students.length > 0 
            ? Math.min(100, Math.round(((presentStudents + lateStudents) / students.length) * 100))
            : 0,
          monthlyData,
          attendanceData
        };
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        throw error;
      }
    },
    retry: 1,
    refetchOnWindowFocus: false
  });

  if (statsError) {
    console.error('Dashboard stats error:', statsError);
    return (
      <Alert variant="destructive" className="mb-4">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load dashboard data. Please try again.
        </AlertDescription>
      </Alert>
    );
  }

  if (statsLoading) {
    return (
      <div className="flex items-center justify-center h-[50vh]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  const totalRevenue = stats?.totalRevenue || 0;
  const attendancePercentage = stats?.attendancePercentage || 0;

  return (
    <div className="space-y-6 p-6 bg-[#F8F9FA]">
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium text-[#343A40]">Students</CardTitle>
            <Users className="h-4 w-4 text-[#20c997]" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#343A40]">{stats?.totalStudents.toLocaleString()}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium text-[#343A40]">Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-[#20c997]" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#343A40]">
              Le {totalRevenue.toLocaleString()}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-[#343A40]">Financial Overview</CardTitle>
              <select className="px-2 py-1 border rounded text-sm">
                <option>{new Date().getFullYear()}</option>
                <option>{new Date().getFullYear() - 1}</option>
              </select>
            </div>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={stats?.monthlyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => `Le ${value.toLocaleString()}`} />
                <Bar dataKey="earnings" fill="#20c997" name="Income" />
                <Bar dataKey="expenses" fill="#dc3545" name="Expenses" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-[#343A40]">Upcoming Exams</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats?.upcomingExams && stats.upcomingExams.length > 0 ? (
                stats.upcomingExams.map((exam) => (
                  <div key={exam.id} className="flex items-center justify-between py-2">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-5 w-5 text-[#20c997]" />
                      <div>
                        <div className="font-medium text-[#343A40]">{exam.name}</div>
                        <div className="text-sm text-gray-500">
                          {exam.subject} - {format(new Date(exam.date), 'MMM dd, yyyy')} at {exam.time}
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-4 text-gray-500">No upcoming exams</div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="text-[#343A40]">Top Performers</CardTitle>
            <Tabs defaultValue="week" className="w-full">
              <TabsList>
                <TabsTrigger value="week">Week</TabsTrigger>
                <TabsTrigger value="month">Month</TabsTrigger>
                <TabsTrigger value="year">Year</TabsTrigger>
              </TabsList>
              <TabsContent value="week" className="space-y-4">
                {stats?.topPerformers && stats.topPerformers.length > 0 ? (
                  stats.topPerformers.map((performer) => (
                    <div key={performer.id} className="flex items-center justify-between py-2">
                      <div className="flex items-center gap-2">
                        <Award className="h-5 w-5 text-[#20c997]" />
                        <div>
                          <div className="font-medium text-[#343A40]">{performer.student?.name || 'Unknown Student'}</div>
                          <div className="text-sm text-gray-500">Grade: {performer.grade}</div>
                        </div>
                      </div>
                      <div className="text-[#20c997] font-medium">{performer.marks}%</div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4 text-gray-500">No exam results available</div>
                )}
              </TabsContent>
              <TabsContent value="month" className="space-y-4">
                <div className="text-center py-4 text-gray-500">Data for monthly view</div>
              </TabsContent>
              <TabsContent value="year" className="space-y-4">
                <div className="text-center py-4 text-gray-500">Data for yearly view</div>
              </TabsContent>
            </Tabs>
          </CardHeader>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-[#343A40]">Today's Attendance Overview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] flex flex-col items-center">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={stats?.attendanceData}
                    innerRadius={60} 
                    outerRadius={80}
                    paddingAngle={5}
                    dataKey="value"
                    nameKey="name"
                  >
                    {stats?.attendanceData.map((entry, index) => {
                      const colors = ['#20c997', '#dc3545', '#fd7e14', '#6f42c1', '#e9ecef'];
                      return <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />;
                    })}
                  </Pie>
                  <Legend />
                  <Tooltip formatter={(value) => value.toString()} />
                </PieChart>
              </ResponsiveContainer>
              <div className="text-center mt-4">
                <div className="text-2xl font-bold text-[#343A40]">{attendancePercentage}%</div>
                <div className="text-sm text-gray-500">Present Today</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
