import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { 
  Plus as AddIcon, 
  Pencil as EditIcon, 
  Trash as DeleteIcon,
  CalendarDays
} from 'lucide-react';

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON>alogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Tabs,
  Ta<PERSON>Content,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { getLevels } from '@/api/levels';
import { getTeachersWithoutSorting } from '@/api/teachers';
import { getSubjects } from '@/api/subjects';
import type { Teacher } from '@/types/user';
import {
  getAllClassTimetable,
  createClassTimetableEntry,
  updateClassTimetableEntry,
  deleteClassTimetableEntry,
  getAllExamTimetable,
  createExamTimetableEntry,
  updateExamTimetableEntry,
  deleteExamTimetableEntry
} from '@/api/timetable';
import { useQuery, useQueryClient } from '@tanstack/react-query';

// Define the timetable entry types locally since they're not exported from the API
interface ClassTimetableEntry {
  id: string;
  subject: string;
  time: string;
  day: string;
  room?: string;
  class_id?: string;
  startTime: string;
  endTime: string;
  teacher: string;
  level?: string;
}

interface ExamTimetableEntry {
  id: string;
  subject: string;
  date: string;
  time: string;
  duration: string;
  room?: string;
  startTime: string;
  endTime: string;
  teacher: string;
  level?: string;
  examDate: string;
  examType: string;
}

interface Level {
  id: string;
  name: string;
  code: string;
  description?: string;
  course_id: string;
  order: number;
  status: 'active' | 'inactive';
  course?: { name: string } | null;
}

interface Subject {
  id: string;
  name: string;
  code: string;
  description?: string;
  courseId: string;
  credits: number;
  status: 'active' | 'inactive';
}

// Type for the dialog props
interface TimetableDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (entry: any) => void;
  entry?: ClassTimetableEntry | ExamTimetableEntry;
  isExamTimetable: boolean;
  levels: Level[];
  teachers: Teacher[];
  subjects: Subject[];
}

const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];

const TimetableDialog: React.FC<TimetableDialogProps> = ({
  open,
  onClose,
  onSubmit,
  entry,
  isExamTimetable,
  levels,
  teachers,
  subjects,
}) => {
  const [formData, setFormData] = useState<Partial<ClassTimetableEntry | ExamTimetableEntry>>(
    entry || {
      day: '',
      startTime: '',
      endTime: '',
      subject: '',
      teacher: '',
      room: '',
      level: '',
      examDate: '',
      examType: '',
    }
  );

  const handleChange = (field: string, value: string) => {
    setFormData({ ...formData, [field]: value });
  };

  const handleSubmit = () => {
    onSubmit(formData);
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>
            {entry ? 'Edit Timetable Entry' : 'Add New Timetable Entry'}
          </DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          {isExamTimetable ? (
            <div className="grid grid-cols-1 gap-4">
              <Input
                type="date"
                value={(formData as Partial<ExamTimetableEntry>).examDate}
                onChange={(e) => handleChange('examDate', e.target.value)}
                placeholder="Exam Date"
              />
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4">
              <Select
                value={(formData as Partial<ClassTimetableEntry>).day}
                onValueChange={(value) => handleChange('day', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select Day" />
                </SelectTrigger>
                <SelectContent>
                  {days.map((day) => (
                    <SelectItem key={day} value={day}>
                      {day}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
          
          <div className="grid grid-cols-2 gap-4">
            <Input
              type="time"
              value={(formData as Partial<ClassTimetableEntry>).startTime}
              onChange={(e) => handleChange('startTime', e.target.value)}
              placeholder="Start Time"
            />
            <Input
              type="time"
              value={(formData as Partial<ClassTimetableEntry>).endTime}
              onChange={(e) => handleChange('endTime', e.target.value)}
              placeholder="End Time"
            />
          </div>
          <div className="grid grid-cols-1 gap-4">
            <Select
              value={(formData as Partial<ClassTimetableEntry>).subject}
              onValueChange={(value) => handleChange('subject', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select Subject" />
              </SelectTrigger>
              <SelectContent>
                {subjects.map((subject) => (
                  <SelectItem key={subject.id} value={subject.id}>
                    {subject.name} ({subject.code})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-1 gap-4">
            <Select
              value={(formData as Partial<ClassTimetableEntry>).teacher}
              onValueChange={(value) => handleChange('teacher', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select Teacher" />
              </SelectTrigger>
              <SelectContent>
                {teachers.map((teacher) => (
                  <SelectItem key={teacher.id} value={teacher.id}>
                    {teacher.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-1 gap-4">
            <Input
              value={(formData as Partial<ClassTimetableEntry>).room}
              onChange={(e) => handleChange('room', e.target.value)}
              placeholder="Room"
            />
          </div>
          <div className="grid grid-cols-1 gap-4">
            <Select
              value={(formData as Partial<ClassTimetableEntry>).level}
              onValueChange={(value) => handleChange('level', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select Level" />
              </SelectTrigger>
              <SelectContent>
                {levels.map((level) => (
                  <SelectItem key={level.id} value={level.id}>
                    {level.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          {isExamTimetable && (
            <div className="grid grid-cols-1 gap-4">
              <Input
                value={(formData as Partial<ExamTimetableEntry>).examType}
                onChange={(e) => handleChange('examType', e.target.value)}
                placeholder="Exam Type"
              />
            </div>
          )}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSubmit}>
            {entry ? 'Update' : 'Add'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

const TimetableManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState("class");
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedEntry, setSelectedEntry] = useState<ClassTimetableEntry | ExamTimetableEntry | undefined>();
  const [levels, setLevels] = useState<Level[]>([]);
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const queryClient = useQueryClient();
  
  // Fetch class timetable data
  const { 
    data: classTimetable = [], 
    isLoading: isClassTimetableLoading 
  } = useQuery({
    queryKey: ['class-timetable'],
    queryFn: getAllClassTimetable,
    enabled: activeTab === "class"
  });
  
  // Fetch exam timetable data
  const { 
    data: examTimetable = [], 
    isLoading: isExamTimetableLoading 
  } = useQuery({
    queryKey: ['exam-timetable'],
    queryFn: getAllExamTimetable,
    enabled: activeTab === "exam"
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const [levelsData, teachersData, subjectsData] = await Promise.all([
          getLevels(),
          getTeachersWithoutSorting(),
          getSubjects()
        ]);
        setLevels(levelsData);
        setTeachers(teachersData);
        setSubjects(subjectsData);
      } catch (error) {
        console.error('Error fetching data:', error);
        toast.error('Failed to load levels, teachers, and subjects data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleAddEntry = () => {
    setSelectedEntry(undefined);
    setDialogOpen(true);
  };

  const handleEditEntry = (entry: ClassTimetableEntry | ExamTimetableEntry) => {
    setSelectedEntry(entry);
    setDialogOpen(true);
  };

  const handleDeleteEntry = async (entryId: string) => {
    if (!window.confirm('Are you sure you want to delete this entry?')) {
      return;
    }
    
    try {
      if (activeTab === "class") {
        await deleteClassTimetableEntry(entryId);
        queryClient.invalidateQueries({ queryKey: ['class-timetable'] });
      } else {
        await deleteExamTimetableEntry(entryId);
        queryClient.invalidateQueries({ queryKey: ['exam-timetable'] });
      }
      toast.success('Entry deleted successfully');
    } catch (error) {
      console.error('Error deleting entry:', error);
      toast.error('Failed to delete entry');
    }
  };

  const handleSubmitEntry = async (entry: Partial<ClassTimetableEntry | ExamTimetableEntry>) => {
    try {
      if (activeTab === "class") {
        const classEntry = entry as Partial<ClassTimetableEntry>;
        
        if (selectedEntry) {
          await updateClassTimetableEntry(selectedEntry.id, classEntry);
          queryClient.invalidateQueries({ queryKey: ['class-timetable'] });
          toast.success('Entry updated successfully');
        } else {
          await createClassTimetableEntry(classEntry as Omit<ClassTimetableEntry, 'id'>);
          queryClient.invalidateQueries({ queryKey: ['class-timetable'] });
          toast.success('Entry added successfully');
        }
      } else {
        const examEntry = entry as Partial<ExamTimetableEntry>;
        
        if (selectedEntry) {
          await updateExamTimetableEntry(selectedEntry.id, examEntry);
          queryClient.invalidateQueries({ queryKey: ['exam-timetable'] });
          toast.success('Entry updated successfully');
        } else {
          await createExamTimetableEntry(examEntry as Omit<ExamTimetableEntry, 'id'>);
          queryClient.invalidateQueries({ queryKey: ['exam-timetable'] });
          toast.success('Entry added successfully');
        }
      }
    } catch (error) {
      console.error('Error submitting entry:', error);
      toast.error(selectedEntry ? 'Failed to update entry' : 'Failed to add entry');
    }
  };

  // Helper function to get level name by ID
  const getLevelName = (levelId: string) => {
    const level = levels.find(l => l.id === levelId);
    return level ? level.name : levelId;
  };

  // Helper function to get teacher name by ID
  const getTeacherName = (teacherId: string) => {
    const teacher = teachers.find(t => t.id === teacherId);
    return teacher ? teacher.name : teacherId;
  };

  const getSubjectName = (subjectId: string) => {
    const subject = subjects.find((s) => s.id === subjectId);
    return subject ? `${subject.name} (${subject.code})` : subjectId;
  };

  const renderTimetableContent = () => {
    const entries = activeTab === "class" ? classTimetable : examTimetable;
    const isEntriesLoading = activeTab === "class" ? isClassTimetableLoading : isExamTimetableLoading;

    if (isEntriesLoading) {
      return (
        <div className="flex justify-center items-center p-8">
          <p className="text-gray-500">Loading timetable data...</p>
        </div>
      );
    }

    return (
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              {activeTab === "class" ? (
                <TableHead>Day</TableHead>
              ) : (
                <TableHead>Exam Date</TableHead>
              )}
              <TableHead>Time</TableHead>
              <TableHead>Subject</TableHead>
              <TableHead>Teacher</TableHead>
              <TableHead>Room</TableHead>
              <TableHead>Level</TableHead>
              {activeTab === "exam" && <TableHead>Exam Type</TableHead>}
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {entries.length === 0 ? (
              <TableRow>
                <TableCell colSpan={activeTab === "class" ? 7 : 8} className="text-center py-4">
                  No entries found. Click the "Add New Entry" button to create one.
                </TableCell>
              </TableRow>
            ) : (
              entries.map((entry) => (
                <TableRow key={entry.id}>
                  <TableCell>
                    {activeTab === "class" 
                      ? (entry as ClassTimetableEntry).day 
                      : (entry as ExamTimetableEntry).examDate}
                  </TableCell>
                  <TableCell>{`${entry.startTime} - ${entry.endTime}`}</TableCell>
                  <TableCell>{getSubjectName(entry.subject)}</TableCell>
                  <TableCell>{getTeacherName(entry.teacher)}</TableCell>
                  <TableCell>{entry.room}</TableCell>
                  <TableCell>{getLevelName(entry.level || '')}</TableCell>
                  {activeTab === "exam" && 
                    <TableCell>{(entry as ExamTimetableEntry).examType}</TableCell>}
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleEditEntry(entry)}
                      >
                        <EditIcon className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDeleteEntry(entry.id)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <DeleteIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    );
  };

  return (
    <div className="w-full space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold tracking-tight">
          Timetable Management
        </h2>
      </div>
      
      <Tabs defaultValue="class" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="class">Class Timetable</TabsTrigger>
          <TabsTrigger value="exam">Exam Timetable</TabsTrigger>
        </TabsList>
        
        <div className="mt-6 mb-4">
          <Button onClick={handleAddEntry} disabled={isLoading}>
            <AddIcon className="mr-2 h-4 w-4" />
            Add New Entry
          </Button>
          {isLoading && <span className="ml-2 text-sm text-muted-foreground">Loading data...</span>}
        </div>

        <TabsContent value="class">
          {renderTimetableContent()}
        </TabsContent>
        
        <TabsContent value="exam">
          {renderTimetableContent()}
        </TabsContent>
      </Tabs>

      {dialogOpen && (
        <TimetableDialog
          open={dialogOpen}
          onClose={() => setDialogOpen(false)}
          onSubmit={handleSubmitEntry}
          entry={selectedEntry}
          isExamTimetable={activeTab === "exam"}
          levels={levels}
          teachers={teachers}
          subjects={subjects}
        />
      )}
    </div>
  );
};

export default TimetableManagement;
