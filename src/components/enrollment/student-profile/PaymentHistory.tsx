import { useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Loader2 } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { getStudentPayments, getStudentPaymentSummary } from '@/api/payments';
import { apiClient } from '@/lib/api-client';
import { cn } from '@/lib/utils';

interface PaymentHistoryProps {
  studentId: string;
}

interface PaymentSummary {
  totalPaid: number;
  courseFee: number;
  remainingBalance: number;
  paymentStatus: string;
}

interface Payment {
  id: string;
  date_paid: string;
  amount: number;
  status: string;
  payment_due_date: string;
  notes?: string;
}

export const PaymentHistory = ({ studentId }: PaymentHistoryProps) => {
  const { data: payments, isLoading: isLoadingPayments } = useQuery<Payment[]>({
    queryKey: ['student-payments', studentId],
    queryFn: () => getStudentPayments(studentId),
    enabled: !!studentId,
    refetchInterval: 30000, // Refetch every 30 seconds for near real-time updates
    staleTime: 10000 // Consider data stale after 10 seconds
  });

  const { data: summary, isLoading: isLoadingSummary } = useQuery<PaymentSummary>({
    queryKey: ['student-payment-summary', studentId],
    queryFn: () => getStudentPaymentSummary(studentId),
    enabled: !!studentId,
    refetchInterval: 30000, // Refetch every 30 seconds for near real-time updates
    staleTime: 10000 // Consider data stale after 10 seconds
  });

  if (isLoadingPayments || isLoadingSummary) {
    return (
      <div className="flex items-center justify-center h-48">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  };

  const formatCurrency = (amount: number) => {
    try {
      return `Le ${amount.toLocaleString()}`;
    } catch (error) {
      console.error('Error formatting currency:', error);
      return `Le ${amount}`;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      case 'partial':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const paymentProgress = summary ? (summary.totalPaid / summary.courseFee) * 100 : 0;

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Course Fee</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(summary?.courseFee || 0)}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Total Paid</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(summary?.totalPaid || 0)}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Remaining</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {formatCurrency(summary?.remainingBalance || 0)}
            </div>
          </CardContent>
        </Card>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Payment Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">Payment Progress:</span>
                <span className="text-sm font-medium">{Math.round(paymentProgress)}%</span>
              </div>
              <Progress value={paymentProgress} className="h-2" />
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500">Status:</span>
              <Badge variant="outline" className={cn(getStatusColor(summary?.paymentStatus || 'pending'))}>
                {summary?.paymentStatus || 'Pending'}
              </Badge>
            </div>
            {summary?.courseFee === 0 && (
              <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-yellow-700 text-sm">
                <p>No course fee found. Please ensure the student is enrolled in a course with a valid fee.</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Payment History</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Due Date</TableHead>
                <TableHead>Notes</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {payments && payments.length > 0 ? (
                payments.map((payment) => (
                  <TableRow key={payment.id}>
                    <TableCell>{formatDate(payment.date_paid)}</TableCell>
                    <TableCell className="font-medium">{formatCurrency(payment.amount)}</TableCell>
                    <TableCell>
                      <Badge variant="outline" className={cn(getStatusColor(payment.status))}>
                        {payment.status}
                      </Badge>
                    </TableCell>
                    <TableCell>{formatDate(payment.payment_due_date)}</TableCell>
                    <TableCell className="text-sm text-gray-500">{payment.notes || '-'}</TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={5} className="text-center text-gray-500">
                    No payment records found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};
