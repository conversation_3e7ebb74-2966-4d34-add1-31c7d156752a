import { useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Loader2 } from "lucide-react";
import { apiClient } from '@/lib/api-client';
import { cn } from '@/lib/utils';
import { getCourses } from '@/api/courses';
import { getLevels } from '@/api/levels';
import { getStudent } from '@/api/students';

interface CourseInfoProps {
  studentId: string;
  courseId?: string;
  levelId?: string;
}

interface Course {
  id: string;
  name: string;
  code: string;
  description: string;
  duration: string;
  fee: number;
  status: 'active' | 'inactive';
}

interface Level {
  id: string;
  name: string;
  code: string;
  description?: string;
  course_id: string;
  order?: number;
  status: 'active' | 'inactive';
}

export const CourseInfo = ({ studentId, courseId: propCourseId, levelId: propLevelId }: CourseInfoProps) => {
  const queryClient = useQueryClient();

  // First, get student details to extract course and level info if not provided
  const { data: student, isLoading: studentLoading } = useQuery({
    queryKey: ['student-for-course-info', studentId],
    queryFn: async () => {
      return await studentModel.getStudentById(studentId);
    },
    enabled: !!studentId && !propCourseId && !propLevelId
  });

  // Use course ID from props or from student data
  const courseId = propCourseId || student?.course_id;
  const levelId = propLevelId || student?.level_id;



  // Fetch course data
  const { data: course, isLoading: courseLoading } = useQuery<Course>({
    queryKey: ['course', courseId],
    queryFn: () => courseModel.getCourseById(courseId!),
    enabled: !!courseId
  });

  // Fetch level data
  const { data: level, isLoading: levelLoading } = useQuery<Level>({
    queryKey: ['level', levelId],
    queryFn: () => levelModel.getLevelById(levelId!),
    enabled: !!levelId
  });

  const isLoading = studentLoading || courseLoading || levelLoading;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!course && !level) {
    return (
      <Card>
        <CardContent className="pt-6">
          <p className="text-center text-muted-foreground">
            No course information available for this student.
          </p>
        </CardContent>
      </Card>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {course && (
        <Card>
          <CardHeader>
            <div className="flex justify-between items-start">
              <CardTitle>Course Information</CardTitle>
              <Badge variant={course.status === 'active' ? 'default' : 'outline'}>
                {course.status.charAt(0).toUpperCase() + course.status.slice(1)}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <dl className="space-y-4">
              <div>
                <dt className="text-sm font-medium text-gray-500">Course Name</dt>
                <dd className="text-lg font-semibold">{course.name}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Course Code</dt>
                <dd className="font-medium">{course.code}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Duration</dt>
                <dd className="font-medium">{course.duration}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Fee</dt>
                <dd className="font-medium">{formatCurrency(course.fee)}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Description</dt>
                <dd className="text-sm text-gray-700">{course.description}</dd>
              </div>
            </dl>
          </CardContent>
        </Card>
      )}

      {level && (
        <Card>
          <CardHeader>
            <div className="flex justify-between items-start">
              <CardTitle>Level Information</CardTitle>
              <Badge variant={level.status === 'active' ? 'default' : 'outline'}>
                {level.status.charAt(0).toUpperCase() + level.status.slice(1)}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <dl className="space-y-4">
              <div>
                <dt className="text-sm font-medium text-gray-500">Level Name</dt>
                <dd className="text-lg font-semibold">{level.name}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Level Code</dt>
                <dd className="font-medium">{level.code}</dd>
              </div>
              {level.order && (
                <div>
                  <dt className="text-sm font-medium text-gray-500">Sequence</dt>
                  <dd className="font-medium">Year {level.order}</dd>
                </div>
              )}
              {level.description && (
                <div>
                  <dt className="text-sm font-medium text-gray-500">Description</dt>
                  <dd className="text-sm text-gray-700">{level.description}</dd>
                </div>
              )}
            </dl>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
