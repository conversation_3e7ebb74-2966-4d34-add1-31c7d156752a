import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { 
  Search, 
  Loader2, 
  IdCard, 
  Edit, 
  Plus,
  RefreshCw,
  Palette,
  Eye
} from "lucide-react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getStudents } from "@/api/students";
import { getCourses } from "@/api/courses";
import { getLevels } from "@/api/levels";
import { updateIdCardStatus, checkAndUpdateExpiredIdCards } from "@/api/students";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export const IdCardManagement = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCourse, setSelectedCourse] = useState("all");
  const [selectedLevel, setSelectedLevel] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [isGeneratingBulk, setIsGeneratingBulk] = useState(false);

  const { data: students = [], isLoading, error } = useQuery({
    queryKey: ['students'],
    queryFn: getStudents,
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 1, // Only retry once to avoid endless loading
    onError: (error: any) => {
      console.error('Error loading students:', error);
      // Show a more helpful error message
      if (error?.code?.includes('permission-denied') || error?.message?.includes('unauthorized')) {
        toast.error('Access denied. Please check your authentication credentials.');
      }
    }
  });

  const { data: courses = [] } = useQuery({
    queryKey: ['courses'],
    queryFn: getCourses,
  });

  const { data: levels = [] } = useQuery({
    queryKey: ['levels'],
    queryFn: getLevels,
  });

  // Check for expired ID cards when component loads
  useEffect(() => {
    const checkExpiredCards = async () => {
      try {
        await checkAndUpdateExpiredIdCards();
        // Refresh the students data after checking for expired cards
        queryClient.invalidateQueries({ queryKey: ['students'] });
      } catch (error) {
        console.error('Error checking expired ID cards:', error);
      }
    };

    checkExpiredCards();
  }, [queryClient]);

  // Get ID card status based on actual data
  const getIdCardStatus = (student: any) => {
    // If no status is set, default to pending
    if (!student.id_card_status) {
      return 'pending';
    }

    // If status is generated, check if it's expired
    if (student.id_card_status === 'generated') {
      if (student.id_card_expiry_date) {
        const expiryDate = new Date(student.id_card_expiry_date);
        const currentDate = new Date();
        
        // If current date is past expiry date, mark as expired
        if (currentDate > expiryDate) {
          return 'expired';
        }
      }
      return 'generated';
    }

    return student.id_card_status;
  };

  // Filter students based on search criteria
  const filteredStudents = (students as any[]).filter((student) => {
    const matchesSearch = 
      student.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.student_id?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCourse = 
      selectedCourse === 'all' || 
      student.course_id === selectedCourse;
    
    const matchesLevel = 
      selectedLevel === 'all' || 
      student.level_id === selectedLevel;
    
    // Get actual ID card status from database
    const idCardStatus = getIdCardStatus(student);
    const matchesStatus = 
      selectedStatus === 'all' || 
      idCardStatus === selectedStatus;
    
    return matchesSearch && matchesCourse && matchesLevel && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'generated':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'expired':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleGenerateIdCard = (studentId: string) => {
    navigate(`/dashboard/enrollment/id-cards/generate/${studentId}`);
  };

  const handleEditIdCard = (studentId: string) => {
    navigate(`/dashboard/enrollment/id-cards/edit/${studentId}`);
  };

  const handleVisualEditIdCard = (studentId: string) => {
    navigate(`/dashboard/enrollment/id-cards/visual-edit/${studentId}`);
  };

  const handleBulkGenerate = async () => {
    setIsGeneratingBulk(true);
    try {
      // Get students that need ID cards generated (pending status)
      const pendingStudents = filteredStudents.filter(s => getIdCardStatus(s) === 'pending');
      
      if (pendingStudents.length === 0) {
        toast.info("No students with pending ID card status found");
        return;
      }

      // Update each student's ID card status to generated
      const updatePromises = pendingStudents.map(student => 
        updateIdCardStatus(student.id, 'generated', 'Dec 2025')
      );

      await Promise.all(updatePromises);
      
      toast.success(`Generated ID cards for ${pendingStudents.length} students`);
      queryClient.invalidateQueries({ queryKey: ['students'] });
    } catch (error) {
      toast.error("Failed to generate ID cards");
    } finally {
      setIsGeneratingBulk(false);
    }
  };

  const stats = {
    total: students.length,
    generated: students.filter(s => getIdCardStatus(s) === 'generated').length,
    pending: students.filter(s => getIdCardStatus(s) === 'pending').length,
    expired: students.filter(s => getIdCardStatus(s) === 'expired').length,
  };

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">ID Card Management</h2>
          <p className="text-muted-foreground">Generate and manage student ID cards</p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={handleBulkGenerate} 
            disabled={isGeneratingBulk || filteredStudents.length === 0}
            className="gap-2"
          >
            {isGeneratingBulk ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            Bulk Generate
          </Button>
          <Button onClick={() => navigate('/dashboard/enrollment/id-cards/settings')}>
            <Plus className="h-4 w-4 mr-2" />
            Card Settings
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Generated</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.generated}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Expired</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.expired}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search students..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Select
          value={selectedCourse}
          onValueChange={(value) => {
            setSelectedCourse(value);
            if (value !== 'all') {
              setSelectedLevel('all');
            }
          }}
        >
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Filter by course" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Courses</SelectItem>
            {courses.map((course: any) => (
              <SelectItem key={course.id} value={course.id}>
                {course.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select
          value={selectedLevel}
          onValueChange={setSelectedLevel}
        >
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Filter by level" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Levels</SelectItem>
            {levels
              .filter((level: any) => selectedCourse === 'all' || level.course_id === selectedCourse)
              .map((level: any) => (
                <SelectItem key={level.id} value={level.id}>
                  {level.name}
                </SelectItem>
              ))}
          </SelectContent>
        </Select>
        <Select
          value={selectedStatus}
          onValueChange={setSelectedStatus}
        >
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="generated">Generated</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="expired">Expired</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Students Table */}
      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Student</TableHead>
              <TableHead>Student ID</TableHead>
              <TableHead>Course</TableHead>
              <TableHead>Level</TableHead>
              <TableHead>ID Card Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredStudents.map((student: any) => {
              const idCardStatus = getIdCardStatus(student);
              return (
                <TableRow key={student.id}>
                  <TableCell className="font-medium">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                        {student.passport_picture ? (
                          <img 
                            src={student.passport_picture} 
                            alt={student.name} 
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <span className="text-gray-500 text-xs font-medium">
                            {student.name.substring(0, 2).toUpperCase()}
                          </span>
                        )}
                      </div>
                      {student.name}
                    </div>
                  </TableCell>
                  <TableCell>{student.student_id}</TableCell>
                  <TableCell>{student.course?.name || 'Not Assigned'}</TableCell>
                  <TableCell>{student.level?.name || 'Not Assigned'}</TableCell>
                  <TableCell>
                    <Badge
                      variant="outline"
                      className={getStatusColor(idCardStatus)}
                    >
                      {idCardStatus.charAt(0).toUpperCase() + idCardStatus.slice(1)}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      {idCardStatus === 'generated' ? (
                        <>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEditIdCard(student.id)}
                            title="Edit ID Card"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleVisualEditIdCard(student.id)}
                            title="Visual Editor"
                            className="text-blue-600 hover:text-blue-700"
                          >
                            <Palette className="h-4 w-4" />
                          </Button>
                        </>
                      ) : (
                        <>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleGenerateIdCard(student.id)}
                            title="Generate ID Card"
                          >
                            <IdCard className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleVisualEditIdCard(student.id)}
                            title="Visual Editor"
                            className="text-blue-600 hover:text-blue-700"
                          >
                            <Palette className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>

      {filteredStudents.length === 0 && (
        <div className="text-center py-8">
          <IdCard className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-semibold text-gray-900">No students found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Try adjusting your search criteria or add new students.
          </p>
        </div>
      )}
    </div>
  );
};

export default IdCardManagement; 