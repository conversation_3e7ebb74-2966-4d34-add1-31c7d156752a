import { Form } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { CreateExamFormProps } from './types';
import { useExamForm } from './hooks/api/useExamForm';
import { ExamFormField } from './components/ExamFormField';
import { CourseSelect } from './components/CourseSelect';
import { LevelSelect } from './components/LevelSelect';

export const CreateExamForm = ({ onSuccess }: CreateExamFormProps) => {
  const {
    form,
    courses,
    levels,
    isLoadingCourses,
    isLoadingLevels,
    createExam
  } = useExamForm(onSuccess);

  const onSubmit = form.handleSubmit((data) => {
    createExam.mutate(data);
  });

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className="space-y-4">
        <ExamFormField
          form={form}
          name="name"
          label="Exam Name"
          placeholder="Enter exam name"
        />

        <ExamFormField
          form={form}
          name="subject"
          label="Subject"
          placeholder="Enter subject name"
        />

        <CourseSelect
          form={form}
          courses={courses || []}
          isLoading={isLoadingCourses}
        />

        <LevelSelect
          form={form}
          levels={levels || []}
          isLoading={isLoadingLevels}
        />

        <ExamFormField
          form={form}
          name="class"
          label="Class"
          placeholder="Enter class name"
        />

        <ExamFormField
          form={form}
          name="type"
          label="Exam Type"
          placeholder="e.g., Midterm, Final"
        />

        <ExamFormField
          form={form}
          name="date"
          label="Date"
          type="date"
        />

        <ExamFormField
          form={form}
          name="time"
          label="Time"
          type="time"
        />

        <Button 
          type="submit" 
          className="w-full"
          disabled={createExam.isPending}
        >
          {createExam.isPending ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating...
            </>
          ) : (
            'Create Exam'
          )}
        </Button>
      </form>
    </Form>
  );
};
