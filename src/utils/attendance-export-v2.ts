import * as XLSX from 'xlsx';
import { format, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from 'date-fns';
import { apiClient } from '@/lib/api-client';
import { toast } from 'sonner';

// Define types for better type safety
interface Student {
  id: string;
  name: string;
  student_id: string;
}

interface Course {
  id: string;
  name: string;
  code: string;
}

interface Level {
  id: string;
  name: string;
  code: string;
}

interface AttendanceRecord {
  id: string;
  date: string;
  student_id: string;
  course_id: string;
  level_id: string;
  status: string;
  notes: string;
}

interface EnrichedAttendanceRecord {
  date: string;
  student_id: string;
  student_name: string;
  course_code: string;
  course_name: string;
  level_code: string;
  level_name: string;
  status: string;
  notes: string;
}

type StudentMap = Record<string, Student>;
type CourseMap = Record<string, Course>;
type LevelMap = Record<string, Level>;

/**
 * Simple and direct implementation of attendance export to Excel
 */
export async function exportAttendance(
  range: 'day' | 'week' | 'month', 
  date: Date,
  courseId?: string,
  levelId?: string
) {
  try {
    toast.info(`Starting export for ${range}...`);
    
    // 1. Determine date range
    let startDate: Date;
    let endDate: Date;
    let filename: string;
    
    switch (range) {
      case 'day':
        startDate = startOfDay(date);
        endDate = endOfDay(date);
        filename = `Attendance_${format(date, 'yyyy-MM-dd')}`;
        break;
      case 'week':
        startDate = startOfWeek(date, { weekStartsOn: 1 });
        endDate = endOfWeek(date, { weekStartsOn: 1 });
        filename = `Attendance_Week_${format(startDate, 'yyyy-MM-dd')}`;
        break;
      case 'month':
        startDate = startOfMonth(date);
        endDate = endOfMonth(date);
        filename = `Attendance_${format(date, 'yyyy-MM')}`;
        break;
      default:
        throw new Error(`Invalid range: ${range}`);
    }
    
    const dateRangeText = `${format(startDate, 'yyyy-MM-dd')} to ${format(endDate, 'yyyy-MM-dd')}`;
    console.log(`Fetching attendance for date range: ${dateRangeText}`);
    
    // 2. Get all students, courses, and levels first for better performance
    const students = await getAllStudents();
    const courses = await getAllCourses();
    const levels = await getAllLevels();
    
    console.log(`Loaded ${Object.keys(students).length} students, ${Object.keys(courses).length} courses, ${Object.keys(levels).length} levels`);
    
    // 3. Get all attendance records within date range
    const attendanceRecords = await getAttendanceRecords(startDate, endDate);
    console.log(`Found ${attendanceRecords.length} attendance records in date range`);
    
    // 4. Filter by course and level if specified
    const filteredRecords = attendanceRecords.filter(record => {
      if (courseId && record.course_id !== courseId) return false;
      if (levelId && record.level_id !== levelId) return false;
      return true;
    });
    
    console.log(`After filtering, ${filteredRecords.length} records remain`);
    
    if (filteredRecords.length === 0) {
      toast.error(`No attendance records found for the selected criteria`);
      return;
    }
    
    // 5. Prepare data for Excel export
    const records: EnrichedAttendanceRecord[] = filteredRecords.map(record => {
      // Get details using our pre-loaded data
      const student = students[record.student_id] || { name: 'Unknown Student', student_id: record.student_id, id: record.student_id };
      const course = courses[record.course_id] || { name: 'Unknown Course', code: record.course_id, id: record.course_id };
      const level = levels[record.level_id] || { name: 'Unknown Level', code: record.level_id, id: record.level_id };
      
      return {
        date: record.date,
        student_id: student.student_id,
        student_name: student.name,
        course_code: course.code,
        course_name: course.name,
        level_code: level.code,
        level_name: level.name,
        status: record.status,
        notes: record.notes || ''
      };
    });
    
    // 6. Create Excel workbook
    const workbook = XLSX.utils.book_new();
    
    // 7. Add summary sheet
    const summaryData = [
      ['Attendance Export Summary'],
      [''],
      ['Date Range', dateRangeText],
      ['Total Records', records.length.toString()],
      ['']
    ];
    
    // Add status breakdown
    summaryData.push(['Status', 'Count', 'Percentage']);
    
    // Count occurrences of each status
    const statusCounts: Record<string, number> = {
      present: records.filter(r => r.status === 'present').length,
      absent: records.filter(r => r.status === 'absent').length,
      late: records.filter(r => r.status === 'late').length,
      excused: records.filter(r => r.status === 'excused').length
    };
    
    for (const [status, count] of Object.entries(statusCounts)) {
      const percentage = (count / records.length * 100).toFixed(2);
      const statusTitle = status.charAt(0).toUpperCase() + status.slice(1);
      summaryData.push([statusTitle, count.toString(), `${percentage}%`]);
    }
    
    // Create and add summary sheet
    const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
    XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');
    
    // Set column widths
    summarySheet['!cols'] = [
      { wch: 15 }, // Column A
      { wch: 15 }, // Column B
      { wch: 15 }  // Column C
    ];
    
    // 8. Add detailed records sheet
    const headers = [
      'Date',
      'Student ID',
      'Student Name',
      'Course Code',
      'Course Name',
      'Level Code',
      'Level Name',
      'Status',
      'Notes'
    ];
    
    // Convert records to array format for XLSX
    const rows = records.map(r => [
      r.date,
      r.student_id,
      r.student_name,
      r.course_code,
      r.course_name,
      r.level_code,
      r.level_name,
      r.status.charAt(0).toUpperCase() + r.status.slice(1), // Capitalize status
      r.notes
    ]);
    
    const detailsData = [headers, ...rows];
    const detailsSheet = XLSX.utils.aoa_to_sheet(detailsData);
    XLSX.utils.book_append_sheet(workbook, detailsSheet, 'Attendance Records');
    
    // Set column widths
    detailsSheet['!cols'] = [
      { wch: 12 }, // Date
      { wch: 12 }, // Student ID
      { wch: 25 }, // Student Name
      { wch: 12 }, // Course Code
      { wch: 25 }, // Course Name
      { wch: 12 }, // Level Code
      { wch: 25 }, // Level Name
      { wch: 10 }, // Status
      { wch: 30 }  // Notes
    ];
    
    // 9. Write file and download
    XLSX.writeFile(workbook, `${filename}.xlsx`);
    
    toast.success('Attendance exported successfully!');
  } catch (error) {
    console.error('Error exporting attendance:', error);
    toast.error(`Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Fetch all students and return a map of id -> student details
 */
async function getAllStudents(): Promise<StudentMap> {
  try {
    const { getStudents } = await import('@/api/students');
    const students = await getStudents();

    const studentMap: StudentMap = {};
    students.forEach(student => {
      studentMap[student.id] = {
        id: student.id,
        name: student.name || 'Unknown',
        student_id: student.student_id || student.id
      };
    });

    return studentMap;
  } catch (error) {
    console.error('Error fetching all students:', error);
    return {};
  }
}

/**
 * Fetch all courses and return a map of id -> course details
 */
async function getAllCourses(): Promise<CourseMap> {
  try {
    const { getCourses } = await import('@/api/courses');
    const courses = await getCourses();

    const courseMap: CourseMap = {};
    courses.forEach(course => {
      courseMap[course.id] = {
        id: course.id,
        name: course.name || 'Unknown Course',
        code: course.code || course.id
      };
    });

    return courseMap;
  } catch (error) {
    console.error('Error fetching all courses:', error);
    return {};
  }
}

/**
 * Fetch all levels and return a map of id -> level details
 */
async function getAllLevels(): Promise<LevelMap> {
  try {
    const { getLevels } = await import('@/api/levels');
    const levels = await getLevels();

    const levelMap: LevelMap = {};
    levels.forEach(level => {
      levelMap[level.id] = {
        id: level.id,
        name: level.name || 'Unknown Level',
        code: level.code || level.id
      };
    });

    return levelMap;
  } catch (error) {
    console.error('Error fetching all levels:', error);
    return {};
  }
}

/**
 * Get attendance records for a date range without using complex queries
 */
async function getAttendanceRecords(startDate: Date, endDate: Date): Promise<AttendanceRecord[]> {
  try {
    const formattedStartDate = format(startDate, 'yyyy-MM-dd');
    const formattedEndDate = format(endDate, 'yyyy-MM-dd');

    // Get attendance records using API
    const { getAttendanceRecords: getAttendance } = await import('@/api/attendance');
    const allRecords = await getAttendance();

    // Filter by date range
    const records: AttendanceRecord[] = allRecords.filter(record => {
      return record.date >= formattedStartDate && record.date <= formattedEndDate;
    });
    
    const records: AttendanceRecord[] = [];
    snapshot.forEach(doc => {
      const data = doc.data();
      
      // Only include records within our date range
      if (data.date >= formattedStartDate && data.date <= formattedEndDate) {
        records.push({
          id: doc.id,
          date: data.date || '',
          student_id: data.student_id || '',
          course_id: data.course_id || '',
          level_id: data.level_id || '',
          status: data.status || 'unknown',
          notes: data.notes || ''
        });
      }
    });
    
    return records;
  } catch (error) {
    console.error('Error fetching attendance records:', error);
    throw error;
  }
} 