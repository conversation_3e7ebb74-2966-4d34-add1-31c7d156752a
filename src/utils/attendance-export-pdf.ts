import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { format, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from 'date-fns';
import { apiClient } from '@/lib/api-client';
import { toast } from 'sonner';

// Define types for better type safety
interface Student {
  id: string;
  name: string;
  student_id: string;
}

interface Course {
  id: string;
  name: string;
  code: string;
}

interface Level {
  id: string;
  name: string;
  code: string;
}

interface AttendanceRecord {
  id: string;
  date: string;
  student_id: string;
  course_id: string;
  level_id: string;
  status: string;
  notes: string;
}

interface EnrichedAttendanceRecord {
  date: string;
  student_id: string;
  student_name: string;
  course_code: string;
  course_name: string;
  level_code: string;
  level_name: string;
  status: string;
  notes: string;
}

type StudentMap = Record<string, Student>;
type CourseMap = Record<string, Course>;
type LevelMap = Record<string, Level>;

/**
 * Exports attendance data to PDF file for day, week, or month
 */
export async function exportAttendanceToPDF(
  range: 'day' | 'week' | 'month', 
  date: Date,
  courseId?: string,
  levelId?: string
) {
  try {
    toast.info(`Preparing ${range} attendance PDF export...`);
    
    // 1. Determine date range
    let startDate: Date;
    let endDate: Date;
    let title: string;
    
    switch (range) {
      case 'day':
        startDate = startOfDay(date);
        endDate = endOfDay(date);
        title = `Attendance_${format(date, 'yyyy-MM-dd')}`;
        break;
      case 'week':
        startDate = startOfWeek(date, { weekStartsOn: 1 });
        endDate = endOfWeek(date, { weekStartsOn: 1 });
        title = `Attendance_Week_${format(startDate, 'yyyy-MM-dd')}_to_${format(endDate, 'yyyy-MM-dd')}`;
        break;
      case 'month':
        startDate = startOfMonth(date);
        endDate = endOfMonth(date);
        title = `Attendance_${format(date, 'yyyy-MM')}`;
        break;
      default:
        throw new Error(`Invalid range: ${range}`);
    }
    
    const dateRangeText = `${format(startDate, 'yyyy-MM-dd')} to ${format(endDate, 'yyyy-MM-dd')}`;
    console.log(`Exporting attendance to PDF for date range: ${dateRangeText}`);
    
    // 2. Get all students, courses, and levels first for better performance
    const students = await getAllStudents();
    const courses = await getAllCourses();
    const levels = await getAllLevels();
    
    console.log(`Loaded ${Object.keys(students).length} students, ${Object.keys(courses).length} courses, ${Object.keys(levels).length} levels`);
    
    // 3. Get all attendance records within date range
    const attendanceRecords = await getAttendanceRecords(startDate, endDate);
    console.log(`Found ${attendanceRecords.length} attendance records in date range`);
    
    // 4. Filter by course and level if specified
    const filteredRecords = attendanceRecords.filter(record => {
      if (courseId && record.course_id !== courseId) return false;
      if (levelId && record.level_id !== levelId) return false;
      return true;
    });
    
    console.log(`After filtering, ${filteredRecords.length} records remain`);
    
    if (filteredRecords.length === 0) {
      toast.error(`No attendance records found for the selected criteria`);
      return;
    }
    
    // 5. Prepare data for PDF export
    const records: EnrichedAttendanceRecord[] = filteredRecords.map(record => {
      // Get details using our pre-loaded data
      const student = students[record.student_id] || { name: 'Unknown Student', student_id: record.student_id, id: record.student_id };
      const course = courses[record.course_id] || { name: 'Unknown Course', code: record.course_id, id: record.course_id };
      const level = levels[record.level_id] || { name: 'Unknown Level', code: record.level_id, id: record.level_id };
      
      return {
        date: record.date,
        student_id: student.student_id,
        student_name: student.name,
        course_code: course.code,
        course_name: course.name,
        level_code: level.code,
        level_name: level.name,
        status: record.status,
        notes: record.notes || ''
      };
    });
    
    // 6. Create new PDF document
    const pdf = new jsPDF();
    
    // 7. Add title and metadata
    // Set title colors
    const headerColor = [34, 100, 34]; // Dark green
    const subheaderColor = [80, 120, 80]; // Lighter green
    
    // Set PDF document properties
    pdf.setProperties({
      title: `Attendance Report - ${range.charAt(0).toUpperCase() + range.slice(1)}`,
      subject: `Attendance data for ${dateRangeText}`,
      creator: 'PTECH',
      author: 'PTECH'
    });
    
    // Add page header on all pages
    const addPageHeader = () => {
      // Add header line
      pdf.setDrawColor(46, 125, 50);
      pdf.setLineWidth(0.5);
      pdf.line(14, 15, pdf.internal.pageSize.width - 14, 15);
      
      // Add title
      pdf.setFontSize(10);
      pdf.setTextColor(46, 125, 50);
      pdf.setFont('helvetica', 'bold');
      pdf.text('PTECH - ATTENDANCE REPORT', 14, 10);
      
      // Add date on top right
      pdf.setFont('helvetica', 'normal');
      pdf.setFontSize(8);
      pdf.text(format(new Date(), 'yyyy-MM-dd'), pdf.internal.pageSize.width - 14, 10, { align: 'right' });
    };
    
    // Add page footer on all pages
    const addPageFooter = (pageNumber: number) => {
      const totalPages = (pdf as any).internal.getNumberOfPages();
      pdf.setDrawColor(46, 125, 50);
      pdf.setLineWidth(0.5);
      pdf.line(14, pdf.internal.pageSize.height - 15, pdf.internal.pageSize.width - 14, pdf.internal.pageSize.height - 15);
      
      pdf.setTextColor(80, 80, 80);
      pdf.setFontSize(8);
      pdf.text(`Page ${pageNumber} of ${totalPages}`, pdf.internal.pageSize.width / 2, pdf.internal.pageSize.height - 10, { align: 'center' });
    };
    
    // Add header to first page
    addPageHeader();
    
    // Add title to the PDF
    pdf.setFontSize(18);
    pdf.setTextColor(headerColor[0], headerColor[1], headerColor[2]);
    pdf.text(`Attendance Report: ${range.charAt(0).toUpperCase() + range.slice(1)}`, 14, 22);
    
    // Add date information
    pdf.setFontSize(10);
    pdf.setTextColor(subheaderColor[0], subheaderColor[1], subheaderColor[2]);
    pdf.text(`Period: ${dateRangeText}`, 14, 30);
    
    // Add course/level filter info if applicable
    let yPosition = 35;
    if (courseId && courses[courseId]) {
      pdf.text(`Course: ${courses[courseId].name}`, 14, yPosition);
      yPosition += 5;
    }
    
    if (levelId && levels[levelId]) {
      pdf.text(`Level: ${levels[levelId].name}`, 14, yPosition);
      yPosition += 5;
    }
    
    // Add export timestamp
    const now = new Date();
    pdf.text(`Generated on: ${format(now, 'yyyy-MM-dd HH:mm:ss')}`, 14, yPosition);
    yPosition += 10;
    
    // 8. Add summary section
    pdf.setFontSize(14);
    pdf.setTextColor(0, 0, 0);
    pdf.text('Attendance Summary', 14, yPosition);
    yPosition += 5;
    
    // Calculate status statistics
    const statusCounts = {
      present: records.filter(r => r.status === 'present').length,
      absent: records.filter(r => r.status === 'absent').length,
      late: records.filter(r => r.status === 'late').length,
      excused: records.filter(r => r.status === 'excused').length
    };
    
    // Create summary table data
    const summaryData = [
      ['Total Records', records.length.toString()],
    ];
    
    // Add status counts
    for (const [status, count] of Object.entries(statusCounts)) {
      if (count > 0) { // Only show statuses that have records
        const percentage = (count / records.length * 100).toFixed(2);
        const statusTitle = status.charAt(0).toUpperCase() + status.slice(1);
        summaryData.push([`${statusTitle}`, `${count} (${percentage}%)`]);
      }
    }
    
    // Add summary table
    autoTable(pdf, {
      startY: yPosition,
      head: [['Item', 'Value']],
      body: summaryData,
      theme: 'grid',
      headStyles: { fillColor: [46, 125, 50], textColor: [255, 255, 255] },
      margin: { top: 30 },
      styles: { overflow: 'linebreak' },
      columnStyles: {
        0: { cellWidth: 40 },
        1: { cellWidth: 40 }
      }
    });
    
    // 9. Add detailed attendance records
    // Get the Y position after the summary table
    yPosition = (pdf as any).lastAutoTable.finalY + 15;
    
    pdf.setFontSize(14);
    pdf.text('Attendance Details', 14, yPosition);
    yPosition += 5;
    
    // Create attendance records table
    const attendanceData = records.map(record => [
      record.date,
      record.student_id,
      record.student_name,
      record.course_name,
      record.level_name,
      record.status.charAt(0).toUpperCase() + record.status.slice(1),
    ]);
    
    // Add the detailed records table
    autoTable(pdf, {
      startY: yPosition,
      head: [['Date', 'Student ID', 'Student Name', 'Course', 'Level', 'Status']],
      body: attendanceData,
      theme: 'striped',
      headStyles: { fillColor: [46, 125, 50], textColor: [255, 255, 255] },
      styles: { overflow: 'linebreak' },
      columnStyles: {
        0: { cellWidth: 20 }, // Date
        1: { cellWidth: 20 }, // Student ID
        2: { cellWidth: 40 }, // Student Name
        3: { cellWidth: 40 }, // Course
        4: { cellWidth: 40 }, // Level
        5: { cellWidth: 20 }, // Status
      }
    });
    
    // Before saving, add headers and footers to all pages
    const totalPages = (pdf as any).internal.getNumberOfPages();
    for (let i = 1; i <= totalPages; i++) {
      pdf.setPage(i);
      if (i > 1) { // We already added header to the first page
        addPageHeader();
      }
      addPageFooter(i);
    }
    
    // 10. Save the PDF
    pdf.save(`${title}.pdf`);
    
    toast.success('Attendance exported successfully as PDF!');
  } catch (error) {
    console.error('Error exporting attendance to PDF:', error);
    toast.error(`PDF export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Fetch all students and return a map of id -> student details
 */
async function getAllStudents(): Promise<StudentMap> {
  try {
    const { getStudents } = await import('@/api/students');
    const students = await getStudents();

    const studentMap: StudentMap = {};
    students.forEach(student => {
      studentMap[student.id] = {
        id: student.id,
        name: student.name || 'Unknown',
        student_id: student.student_id || student.id
      };
    });

    return studentMap;
  } catch (error) {
    console.error('Error fetching all students:', error);
    return {};
  }
}

/**
 * Fetch all courses and return a map of id -> course details
 */
async function getAllCourses(): Promise<CourseMap> {
  try {
    const { getCourses } = await import('@/api/courses');
    const courses = await getCourses();

    const courseMap: CourseMap = {};
    courses.forEach(course => {
      courseMap[course.id] = {
        id: course.id,
        name: course.name || 'Unknown Course',
        code: course.code || course.id
      };
    });

    return courseMap;
  } catch (error) {
    console.error('Error fetching all courses:', error);
    return {};
  }
}

/**
 * Fetch all levels and return a map of id -> level details
 */
async function getAllLevels(): Promise<LevelMap> {
  try {
    const { getLevels } = await import('@/api/levels');
    const levels = await getLevels();

    const levelMap: LevelMap = {};
    levels.forEach(level => {
      levelMap[level.id] = {
        id: level.id,
        name: level.name || 'Unknown Level',
        code: level.code || level.id
      };
    });

    return levelMap;
  } catch (error) {
    console.error('Error fetching all levels:', error);
    return {};
  }
}

/**
 * Get attendance records for a date range without using complex queries
 */
async function getAttendanceRecords(startDate: Date, endDate: Date): Promise<AttendanceRecord[]> {
  try {
    const startDateStr = format(startDate, 'yyyy-MM-dd');
    const endDateStr = format(endDate, 'yyyy-MM-dd');

    // Get attendance records using API
    const { getAttendanceRecords: getAttendance } = await import('@/api/attendance');
    const allRecords = await getAttendance();

    // Filter by date range
    const records: AttendanceRecord[] = allRecords.filter(record => {
      return record.date >= startDateStr && record.date <= endDateStr;
    });
    
    snapshot.forEach(doc => {
      const data = doc.data();
      
      // Only include records within our date range
      if (data.date >= startDateStr && data.date <= endDateStr) {
        records.push({
          id: doc.id,
          date: data.date || '',
          student_id: data.student_id || '',
          course_id: data.course_id || '',
          level_id: data.level_id || '',
          status: data.status || '',
          notes: data.notes || ''
        });
      }
    });
    
    return records;
  } catch (error) {
    console.error('Error fetching attendance records:', error);
    return [];
  }
} 