import * as XLSX from 'xlsx';
import { format, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from 'date-fns';
import { apiClient } from '@/lib/api-client';
import { toast } from 'sonner';
import { getAttendanceRecords } from '@/api/attendance';
import { getStudents } from '@/api/students';
import { getCourses } from '@/api/courses';
import { getLevels } from '@/api/levels';

/**
 * Simple attendance export that directly queries all relevant data without requiring complex indices
 */
export const exportAttendanceToExcel = async (
  range: 'day' | 'week' | 'month',
  date = new Date(),
  courseId?: string,
  levelId?: string
) => {
  try {
    toast.info(`Preparing ${range} attendance export...`);
    
    // 1. Determine date range
    let startDate: Date;
    let endDate: Date;
    let title: string;
    
    switch (range) {
      case 'day':
        startDate = startOfDay(date);
        endDate = endOfDay(date);
        title = `Attendance_${format(date, 'yyyy-MM-dd')}`;
        break;
      case 'week':
        startDate = startOfWeek(date, { weekStartsOn: 1 }); // Monday as week start
        endDate = endOfWeek(date, { weekStartsOn: 1 });
        title = `Attendance_Week_${format(startDate, 'yyyy-MM-dd')}_to_${format(endDate, 'yyyy-MM-dd')}`;
        break;
      case 'month':
        startDate = startOfMonth(date);
        endDate = endOfMonth(date);
        title = `Attendance_${format(date, 'yyyy-MM')}`;
        break;
      default:
        throw new Error('Invalid range specified');
    }
    
    const formattedStartDate = format(startDate, 'yyyy-MM-dd');
    const formattedEndDate = format(endDate, 'yyyy-MM-dd');
    
    console.log(`Exporting attendance from ${formattedStartDate} to ${formattedEndDate}`);

    // 2. Query attendance records using API
    const allRecords = await getAttendanceRecords(undefined, courseId, levelId);

    // Filter records by date range
    const filteredRecords = allRecords.filter(record => {
      const recordDate = record.date;
      return recordDate >= formattedStartDate && recordDate <= formattedEndDate;
    });

    if (filteredRecords.length === 0) {
      toast.error(`No attendance records found for the selected ${range}`);
      return;
    }

    console.log(`Found ${filteredRecords.length} attendance records in date range`);

    // 3. Get all students, courses, and levels for reference
    const [allStudents, allCourses, allLevels] = await Promise.all([
      getStudents(),
      getCourses(),
      getLevels()
    ]);

    // Create lookup maps
    const studentMap = new Map(allStudents.map(s => [s.id, s]));
    const courseMap = new Map(allCourses.map(c => [c.id, c]));
    const levelMap = new Map(allLevels.map(l => [l.id, l]));

    // 4. Process records and add related data
    const records = filteredRecords.map(record => {
      const student = studentMap.get(record.student_id);
      const course = courseMap.get(record.course_id || '');
      const level = levelMap.get(record.level_id || '');

      return {
        ...record,
        student_name: student?.name || 'Unknown',
        student_number: student?.student_id || record.student_id,
        course_name: course?.name || 'Unknown Course',
        course_code: course?.code || '',
        level_name: level?.name || 'Unknown Level',
        level_code: level?.code || ''
      };
    });

    console.log(`After processing: ${records.length} attendance records ready for export`);

    // 5. Create Excel workbook
    const wb = XLSX.utils.book_new();

    // 6. Create attendance summary sheet
    const summaryData = [
      ['Attendance Export Summary'],
      [''],
      ['Date Range', `${formattedStartDate} to ${formattedEndDate}`],
      ['Total Records', `${records.length}`],
      [''],
      ['Status', 'Count', 'Percentage'],
    ];
    
    // Calculate statistics
    const statuses = ['present', 'absent', 'late', 'excused'];
    for (const status of statuses) {
      const count = records.filter(r => r.status === status).length;
      const percentage = records.length > 0 ? (count / records.length) * 100 : 0;
      summaryData.push([
        status.charAt(0).toUpperCase() + status.slice(1),
        count.toString(),
        `${percentage.toFixed(2)}%`
      ]);
    }
    
    const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
    XLSX.utils.book_append_sheet(wb, summarySheet, 'Summary');
    
    // Set column widths for summary sheet
    summarySheet['!cols'] = [
      { wch: 15 }, // Column A
      { wch: 15 }, // Column B
      { wch: 15 }, // Column C
    ];

    // 7. Create detailed attendance records sheet
    const headers = [
      'Date',
      'Student ID',
      'Student Name',
      'Course Code',
      'Course Name',
      'Level Code',
      'Level Name',
      'Status',
      'Notes'
    ];
    
    const rows = records.map(record => [
      record.date,
      record.student_number,
      record.student_name,
      record.course_code,
      record.course_name,
      record.level_code,
      record.level_name,
      record.status.charAt(0).toUpperCase() + record.status.slice(1), // Capitalize status
      record.notes
    ]);
    
    const detailsData = [headers, ...rows];
    const detailsSheet = XLSX.utils.aoa_to_sheet(detailsData);
    XLSX.utils.book_append_sheet(wb, detailsSheet, 'Attendance Records');
    
    // Set column widths for details sheet
    detailsSheet['!cols'] = [
      { wch: 12 }, // Date
      { wch: 12 }, // Student ID
      { wch: 25 }, // Student Name
      { wch: 12 }, // Course Code
      { wch: 25 }, // Course Name
      { wch: 12 }, // Level Code
      { wch: 25 }, // Level Name
      { wch: 10 }, // Status
      { wch: 30 }, // Notes
    ];

    // 8. Write to file and download
    console.log("Writing Excel file...");
    XLSX.writeFile(wb, `${title}.xlsx`);
    
    toast.success(`Attendance exported successfully for the ${range}`);
  } catch (error) {
    console.error('Error exporting attendance:', error);
    toast.error(`Failed to export attendance: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}; 